# TEE_CheckMemoryAccessRights GP标准完整检查清单

## 1. GP标准强制要求检查项

### 1.1 基础参数检查 ✅
- [ ] **size为0检查**：size为0时直接返回TEE_SUCCESS
- [ ] **NULL指针检查**：buffer为NULL且size>0时返回TEE_ERROR_BAD_PARAMETERS
- [ ] **访问标志有效性**：必须包含READ或WRITE标志之一
- [ ] **保留标志检查**：除READ/WRITE/ANY_OWNER外的标志必须为0
- [ ] **地址溢出检查**：buffer + size < buffer时返回错误

### 1.2 MMU权限检查 ✅
- [ ] **页面级权限验证**：检查每个页面的MMU权限
- [ ] **读权限验证**：TEE_MEMORY_ACCESS_READ标志对应的MMU权限
- [ ] **写权限验证**：TEE_MEMORY_ACCESS_WRITE标志对应的MMU权限
- [ ] **用户空间检查**：确保地址在用户空间范围内

### 1.3 内存类型和最小权限检查 ✅
- [ ] **TA堆内存**：TEE_Malloc分配的内存必须有读写权限
- [ ] **TA栈内存**：TA栈必须有读写权限
- [ ] **TA数据段**：非const变量必须有读写权限
- [ ] **TA常量段**：const变量必须有读权限，不应有写权限
- [ ] **TA代码段**：代码段可能有读权限，必须有执行权限
- [ ] **TEE_Param结构**：必须有读写权限
- [ ] **CA共享内存**：权限必须与TEE_PARAM_TYPES对应

### 1.4 所有权和共享检查 ✅
- [ ] **ANY_OWNER标志处理**：
  - 未设置时：检查缓冲区是否未共享
  - 已设置时：不检测缓冲区创建者
- [ ] **共享内存检测**：识别与CA共享的内存
- [ ] **所有权验证**：确保内存由TA或更可信组件拥有
- [ ] **信任级别检查**：防止低信任组件控制内存

### 1.5 一致性保证检查 ✅
- [ ] **读一致性**：两次连续读取返回相同数据
- [ ] **读写一致性**：写后读能读到写入的数据
- [ ] **不可观察性**：写入数据不被低信任组件观察
- [ ] **共享内存拒绝**：真正共享内存必须返回ACCESS_DENIED

### 1.6 堆内存安全检查 ✅
- [ ] **堆重叠检查**：检查是否与堆内存重叠
- [ ] **已分配验证**：确保在已分配的堆块内
- [ ] **分配器无关**：支持dlmalloc和scudo
- [ ] **brk边界检查**：不超出当前brk边界

## 2. GP标准返回值要求

### 2.1 成功返回保证 ✅
- [ ] **TEE_SUCCESS语义**：整个缓冲区允许请求的访问
- [ ] **后续访问保证**：返回成功后的读写操作必定成功
- [ ] **无Panic保证**：后续访问不会触发Panic

### 2.2 失败返回语义 ✅
- [ ] **TEE_ERROR_ACCESS_DENIED**：至少一个字节不具有请求权限
- [ ] **TEE_ERROR_BAD_PARAMETERS**：参数无效

## 3. GP标准特殊行为要求

### 3.1 NULL指针行为 ✅
- [ ] **NULL指针保护**：NULL指针不能被解引用
- [ ] **Panic要求**：尝试读取NULL地址必须Panic
- [ ] **地址段扩展**：从NULL开始的实现定义地址段

### 3.2 无Panic保证 ✅
- [ ] **函数无Panic**：TEE_CheckMemoryAccessRights不能因任何原因Panic
- [ ] **异常处理**：所有错误情况都通过返回值处理

### 3.3 不访问缓冲区 ✅
- [ ] **地址检查**：基于地址和内存管理信息检查
- [ ] **避免解引用**：不实际访问缓冲区内容

## 4. GP标准有效性期限

### 4.1 检测结果失效条件 ✅
- [ ] **内存操作失效**：TEE_Realloc或TEE_Free调用后失效
- [ ] **TA返回失效**：TA入口点返回后失效
- [ ] **权限变更失效**：外部实体修改共享内存权限后失效

## 5. OP-TEE对照检查

### 5.1 OP-TEE实现的检查项
- [ ] **malloc_buffer_overlaps_heap()**：堆重叠检查
- [ ] **malloc_buffer_is_within_alloced()**：已分配检查
- [ ] **check_mem_access_rights_params()**：参数权限检查
- [ ] **tee_mmu_user_va2pa_attr()**：MMU权限查询

### 5.2 Trusty适配对应
- [ ] **trusty_buffer_overlaps_heap()**：使用brk系统调用
- [ ] **trusty_buffer_is_within_allocated()**：分配器无关实现
- [ ] **kernel_check_mmu_permissions()**：arch_mmu_query()
- [ ] **gp_standard_*_check()**：完整GP标准检查

## 6. 实现完整性验证（OP-TEE风格分层）

### 6.1 用户层检查（像OP-TEE一样）✅
```
用户空间 TEE_CheckMemoryAccessRights():
1. 基础参数检查 (size=0, NULL指针, 标志有效性)
2. 堆内存安全检查 (堆重叠, 已分配验证)
3. TA参数兼容性检查 (check_mem_access_rights_params)
4. 调用系统调用进行MMU权限检查
```

### 6.2 内核层检查（必须在内核的）✅
```
内核空间 sys_check_memory_access_rights():
1. MMU权限查询 (arch_mmu_query)
2. 内存映射验证 (VMM状态检查)
3. 地址空间边界检查
4. 特权级别验证
```

### 6.3 分层设计原则 ✅
- [ ] **用户层优先**：能在用户层做的检查都在用户层完成
- [ ] **内核层必要**：只有需要特权访问的检查才在内核层
- [ ] **分配器感知**：用户层可以调用分配器特定函数
- [ ] **性能优化**：减少系统调用开销

### 6.3 GP标准兼容性 ✅
- [ ] **完整标志支持**：READ/WRITE/ANY_OWNER
- [ ] **完整错误码**：SUCCESS/ACCESS_DENIED/BAD_PARAMETERS
- [ ] **完整语义实现**：所有GP标准要求的检查

## 7. 测试验证要求

### 7.1 功能测试
- [ ] **基础参数测试**：NULL指针、零大小、无效标志
- [ ] **权限测试**：读写权限组合测试
- [ ] **内存类型测试**：堆、栈、代码、常量段测试
- [ ] **共享内存测试**：ANY_OWNER标志测试

### 7.2 安全测试
- [ ] **堆安全测试**：未分配堆内存访问测试
- [ ] **权限绕过测试**：尝试绕过权限检查
- [ ] **一致性测试**：读写一致性验证

### 7.3 兼容性测试
- [ ] **分配器测试**：dlmalloc和scudo配置测试
- [ ] **GP标准测试**：与标准测试套件对比
- [ ] **OP-TEE对比**：与OP-TEE行为对比

这个检查清单确保Trusty的TEE_CheckMemoryAccessRights实现完全符合GP标准要求。
