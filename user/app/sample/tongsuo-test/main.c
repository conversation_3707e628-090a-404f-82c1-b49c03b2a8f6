#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <trusty_log.h>
#include <err.h>

// Tongsuo headers with TONGSUO_ prefix
#include <openssl/evp.h>
#include <openssl/rand.h>
#include <crypto/sm2.h>
#include <openssl/sm3.h>
#include <crypto/sm4.h>
#include <openssl/aes.h>
#include <openssl/sha.h>

#define LOG_TAG "tongsuo-test"

static void test_random_generation(void) {
    unsigned char buffer[32];
    int ret;
    
    printf("%s: Testing random number generation...\n", LOG_TAG);
    
    ret = RAND_bytes(buffer, sizeof(buffer));
    if (ret == 1) {
        printf("%s: Random generation SUCCESS\n", LOG_TAG);
        printf("%s: First 8 bytes: ", LOG_TAG);
        for (int i = 0; i < 8; i++) {
            printf("%02x ", buffer[i]);
        }
        printf("\n");
    } else {
        printf("%s: Random generation FAILED\n", LOG_TAG);
    }
}

static void test_aes_encryption(void) {
    printf("%s: Testing AES encryption...\n", LOG_TAG);
    
    const unsigned char key[16] = {
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f
    };
    
    const unsigned char plaintext[16] = {
        0x00, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77,
        0x88, 0x99, 0xaa, 0xbb, 0xcc, 0xdd, 0xee, 0xff
    };
    
    unsigned char ciphertext[16];
    unsigned char decrypted[16];
    AES_KEY aes_key;
    
    // Set up AES key
    if (AES_set_encrypt_key(key, 128, &aes_key) != 0) {
        printf("%s: AES key setup FAILED\n", LOG_TAG);
        return;
    }
    
    // Encrypt
    AES_encrypt(plaintext, ciphertext, &aes_key);
    
    // Set up for decryption
    if (AES_set_decrypt_key(key, 128, &aes_key) != 0) {
        printf("%s: AES decrypt key setup FAILED\n", LOG_TAG);
        return;
    }
    
    // Decrypt
    AES_decrypt(ciphertext, decrypted, &aes_key);
    
    // Verify
    if (memcmp(plaintext, decrypted, 16) == 0) {
        printf("%s: AES encryption/decryption SUCCESS\n", LOG_TAG);
    } else {
        printf("%s: AES encryption/decryption FAILED\n", LOG_TAG);
    }
}

static void test_sha256_hash(void) {
    printf("%s: Testing SHA256 hash...\n", LOG_TAG);
    
    const char *message = "Hello, Tongsuo in Trusty TEE!";
    unsigned char hash[SHA256_DIGEST_LENGTH];
    
    SHA256_CTX ctx;
    if (SHA256_Init(&ctx) != 1) {
        printf("%s: SHA256 init FAILED\n", LOG_TAG);
        return;
    }
    
    if (SHA256_Update(&ctx, message, strlen(message)) != 1) {
        printf("%s: SHA256 update FAILED\n", LOG_TAG);
        return;
    }
    
    if (SHA256_Final(hash, &ctx) != 1) {
        printf("%s: SHA256 final FAILED\n", LOG_TAG);
        return;
    }
    
    printf("%s: SHA256 hash SUCCESS\n", LOG_TAG);
    printf("%s: Hash: ", LOG_TAG);
    for (int i = 0; i < SHA256_DIGEST_LENGTH; i++) {
        printf("%02x", hash[i]);
    }
    printf("\n");
}

static void test_sm3_hash(void) {
    printf("%s: Testing SM3 hash (Chinese national algorithm)...\n", LOG_TAG);
    
    const char *message = "Hello, SM3 in Tongsuo!";
    unsigned char hash[SM3_DIGEST_LENGTH];
    
    SM3_CTX ctx;
    if (SM3_Init(&ctx) != 1) {
        printf("%s: SM3 init FAILED\n", LOG_TAG);
        return;
    }
    
    if (SM3_Update(&ctx, message, strlen(message)) != 1) {
        printf("%s: SM3 update FAILED\n", LOG_TAG);
        return;
    }
    
    if (SM3_Final(hash, &ctx) != 1) {
        printf("%s: SM3 final FAILED\n", LOG_TAG);
        return;
    }
    
    printf("%s: SM3 hash SUCCESS\n", LOG_TAG);
    printf("%s: Hash: ", LOG_TAG);
    for (int i = 0; i < SM3_DIGEST_LENGTH; i++) {
        printf("%02x", hash[i]);
    }
    printf("\n");
}

static void test_sm4_encryption(void) {
    printf("%s: Testing SM4 encryption (Chinese national algorithm)...\n", LOG_TAG);
    
    const unsigned char key[16] = {
        0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef,
        0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10
    };
    
    const unsigned char plaintext[16] = {
        0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef,
        0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10
    };
    
    unsigned char ciphertext[16];
    unsigned char decrypted[16];
    SM4_KEY sm4_key;
    
    // Set up SM4 key
    if (SM4_set_key(key, &sm4_key) != 0) {
        printf("%s: SM4 key setup FAILED\n", LOG_TAG);
        return;
    }
    
    // Encrypt
    SM4_encrypt(plaintext, ciphertext, &sm4_key);
    
    // Decrypt
    SM4_decrypt(ciphertext, decrypted, &sm4_key);
    
    // Verify
    if (memcmp(plaintext, decrypted, 16) == 0) {
        printf("%s: SM4 encryption/decryption SUCCESS\n", LOG_TAG);
    } else {
        printf("%s: SM4 encryption/decryption FAILED\n", LOG_TAG);
    }
}

static void test_evp_interface(void) {
    printf("%s: Testing EVP high-level interface...\n", LOG_TAG);
    
    EVP_MD_CTX *ctx = EVP_MD_CTX_new();
    if (!ctx) {
        printf("%s: EVP_MD_CTX_new FAILED\n", LOG_TAG);
        return;
    }
    
    const char *message = "EVP interface test";
    unsigned char hash[EVP_MAX_MD_SIZE];
    unsigned int hash_len;
    
    if (EVP_DigestInit_ex(ctx, EVP_sha256(), NULL) != 1) {
        printf("%s: EVP_DigestInit_ex FAILED\n", LOG_TAG);
        EVP_MD_CTX_free(ctx);
        return;
    }
    
    if (EVP_DigestUpdate(ctx, message, strlen(message)) != 1) {
        printf("%s: EVP_DigestUpdate FAILED\n", LOG_TAG);
        EVP_MD_CTX_free(ctx);
        return;
    }
    
    if (EVP_DigestFinal_ex(ctx, hash, &hash_len) != 1) {
        printf("%s: EVP_DigestFinal_ex FAILED\n", LOG_TAG);
        EVP_MD_CTX_free(ctx);
        return;
    }
    
    EVP_MD_CTX_free(ctx);
    
    printf("%s: EVP interface SUCCESS\n", LOG_TAG);
    printf("%s: Hash length: %u\n", LOG_TAG, hash_len);
}

int main(void) {
    printf("%s: Starting Tongsuo cryptographic library tests\n", LOG_TAG);
    printf("%s: ==========================================\n", LOG_TAG);
    
    // Test basic cryptographic functions
    test_random_generation();
    test_aes_encryption();
    test_sha256_hash();
    test_evp_interface();
    
    // Test Chinese national algorithms
    test_sm3_hash();
    test_sm4_encryption();
    
    printf("%s: ==========================================\n", LOG_TAG);
    printf("%s: All tests completed\n", LOG_TAG);
    
    return 0;
}
