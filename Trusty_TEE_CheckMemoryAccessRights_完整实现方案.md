# Trusty TEE_CheckMemoryAccessRights 完整实现方案

## 概述

本方案为Trusty-TEE实现符合GP标准的TEE_CheckMemoryAccessRights函数，采用严格的用户/内核分离架构，确保用户空间不会直接访问内核接口。

## 1. 架构设计

### 1.1 三层检查机制
1. **MMU权限检查**：通过系统调用在内核侧进行
2. **参数权限检查**：在用户空间检查与TA参数的兼容性
3. **堆内存安全检查**：防止TA暴露私有堆内存

### 1.2 调用流程
```
用户空间: TEE_CheckMemoryAccessRights()
    ↓
系统调用: _rctee_check_memory_access_rights()
    ↓
内核处理: sys_check_memory_access_rights()
    ↓
权限检查: trusty_check_memory_access_rights()
    ↓
MMU查询: get_memory_attributes() (使用现有Trusty接口)
```

## 2. 用户空间实现

### 2.1 主函数实现 (lib/libutee/tee_api.c)

```c
#include <tee_internal_api.h>
#include <tee_internal_api_extensions.h>
#include <rctee_syscalls.h>  // 自动生成的系统调用头文件
#include <lib/dlmalloc.h>    // dlmalloc函数
#include <brk_syscalls.h>    // brk系统调用

/**
 * GP标准TEE_CheckMemoryAccessRights实现
 */
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size)
{
    uint32_t flags = accessFlags;
    
    /* GP标准：size为0时直接返回成功 */
    if (!size)
        return TEE_SUCCESS;
    
    /*
     * 第一步：通过系统调用检查内存映射权限
     * 这是核心的MMU权限检查，由内核完成
     */
    if (_rctee_check_memory_access_rights(accessFlags, buffer, size))
        return TEE_ERROR_ACCESS_DENIED;
    
    /*
     * 第二步：检查与TA参数的权限兼容性
     * 清除扩展标志，只保留基本访问标志
     */
    flags &= TEE_MEMORY_ACCESS_READ | TEE_MEMORY_ACCESS_WRITE |
             TEE_MEMORY_ACCESS_ANY_OWNER;
    if (check_mem_access_rights_params(flags, buffer, size))
        return TEE_ERROR_ACCESS_DENIED;
    
    /*
     * 第三步：检查堆内存重叠（如果适用）
     * 防止TA暴露私有堆内存
     */
    if (trusty_buffer_overlaps_heap(buffer, size) &&
        !trusty_buffer_is_within_allocated(buffer, size))
        return TEE_ERROR_ACCESS_DENIED;
    
    return TEE_SUCCESS;
}
```

### 2.2 辅助函数实现

```c
/**
 * 检查内存访问权限的辅助函数 - 检查与TA参数的重叠
 */
static TEE_Result check_mem_access_rights_params(uint32_t flags, void *buf, size_t len)
{
    size_t n = 0;
    
    for (n = 0; n < TEE_NUM_PARAMS; n++) {
        uint32_t f = TEE_MEMORY_ACCESS_ANY_OWNER;
        
        switch (TEE_PARAM_TYPE_GET(ta_param_types, n)) {
        case TEE_PARAM_TYPE_MEMREF_OUTPUT:
        case TEE_PARAM_TYPE_MEMREF_INOUT:
            f |= TEE_MEMORY_ACCESS_WRITE;
            fallthrough;
        case TEE_PARAM_TYPE_MEMREF_INPUT:
            f |= TEE_MEMORY_ACCESS_READ;
            if (bufs_intersect(buf, len,
                              ta_params[n].memref.buffer,
                              ta_params[n].memref.size)) {
                if ((flags & f) != flags)
                    return TEE_ERROR_ACCESS_DENIED;
            }
            break;
        default:
            break;
        }
    }
    
    return TEE_SUCCESS;
}

/**
 * 检查缓冲区是否重叠
 */
static bool bufs_intersect(void *buf1, size_t sz1, void *buf2, size_t sz2)
{
    vaddr_t b1 = (vaddr_t)buf1;
    vaddr_t b2 = (vaddr_t)buf2;
    vaddr_t e1 = b1 + sz1 - 1;
    vaddr_t e2 = b2 + sz2 - 1;
    
    if (!sz1 || !sz2)
        return false;
    
    if (e1 < b2 || e2 < b1)
        return false;
    
    return true;
}
```

## 3. 系统调用接口

### 3.1 系统调用定义 (kernel/rctee/lib/rctee/include/syscall_table.h)

```c
/**
 * 内存访问权限检查系统调用
 * 系统调用号：0x42 (在现有系统调用表后添加)
 * 参数：flags - 访问标志 (TEE_MEMORY_ACCESS_*)
 *       buf - 要检查的缓冲区地址
 *       len - 缓冲区长度
 * 返回：long - TEE_SUCCESS 或 TEE_ERROR_ACCESS_DENIED
 */
DEF_SYSCALL(0x42, check_memory_access_rights, long, 3, uint32_t flags, const void *buf, size_t len)
```

### 3.2 用户空间系统调用函数

系统调用定义后，Trusty的stubgen工具会自动生成用户空间函数：
```c
// 自动生成在 $(BUILDDIR)/generated/user/base/lib/syscall-stubs/rctee_syscalls.h
long _rctee_check_memory_access_rights(uint32_t flags, const void *buf, size_t len);
```

## 4. 内核实现

### 4.1 系统调用处理函数 (kernel/rctee/lib/rctee/rctee_core/syscall.c)

```c
/**
 * 系统调用：检查内存访问权限
 * 对应DEF_SYSCALL中的check_memory_access_rights
 */
static long sys_check_memory_access_rights(uint32_t flags, const void *buf, size_t len)
{
    TEE_Result res;
    vaddr_t buf_addr = (vaddr_t)buf;

    /* 参数验证 */
    if (!len)
        return TEE_SUCCESS;

    if (!buf)
        return TEE_ERROR_BAD_PARAMETERS;

    /* 检查地址溢出 */
    if (buf_addr + len < buf_addr)
        return TEE_ERROR_BAD_PARAMETERS;

    /* 调用内核内存权限检查函数 */
    res = trusty_check_memory_access_rights(flags, buf, len);

    return res;
}
```

### 4.2 内核内存权限检查实现 (kernel/trusty_memory_check.c)

```c
/**
 * Trusty内核内存访问权限检查实现
 */
TEE_Result trusty_check_memory_access_rights(uint32_t flags, void *buffer, size_t size)
{
    vaddr_t addr = (vaddr_t)buffer;
    vaddr_t end_addr;
    size_t check_size = PAGE_SIZE;  /* 按页检查 */

    /* 检查地址溢出 */
    if (addr + size < addr)
        return TEE_ERROR_ACCESS_DENIED;

    end_addr = addr + size;

    /* 逐页检查内存权限 */
    for (vaddr_t va = ROUND_DOWN(addr, PAGE_SIZE);
         va < end_addr;
         va += check_size) {

        uint32_t mmu_flags;
        TEE_Result res;

        /* 获取MMU权限属性 */
        res = get_memory_attributes(va, &mmu_flags);
        if (res != TEE_SUCCESS)
            return TEE_ERROR_ACCESS_DENIED;

        /* 检查读权限 */
        if ((flags & TEE_MEMORY_ACCESS_READ) &&
            !(mmu_flags & MMU_FLAG_PERM_USER_READ))
            return TEE_ERROR_ACCESS_DENIED;

        /* 检查写权限 */
        if ((flags & TEE_MEMORY_ACCESS_WRITE) &&
            !(mmu_flags & MMU_FLAG_PERM_USER_WRITE))
            return TEE_ERROR_ACCESS_DENIED;

        /* 检查所有者权限 */
        if (!(flags & TEE_MEMORY_ACCESS_ANY_OWNER)) {
            if (!is_ta_private_memory(va))
                return TEE_ERROR_ACCESS_DENIED;
        }
    }

    return TEE_SUCCESS;
}

/**
 * 获取内存属性的内核函数
 */
static TEE_Result get_memory_attributes(vaddr_t va, uint32_t *flags)
{
    /* 使用现有的Trusty MMU查询接口 */
    uint arch_mmu_flags;
    int ret;

    ret = arch_mmu_query(&current_aspace->arch_aspace, va, NULL, &arch_mmu_flags);
    if (ret < 0)
        return TEE_ERROR_ACCESS_DENIED;

    /* 转换Trusty MMU标志到TEE标志 */
    *flags = 0;
    if (arch_mmu_flags & ARCH_MMU_FLAG_PERM_USER)
        *flags |= MMU_FLAG_PERM_USER_READ;
    if (arch_mmu_flags & ARCH_MMU_FLAG_PERM_RO)
        *flags &= ~MMU_FLAG_PERM_USER_WRITE;
    else if (arch_mmu_flags & ARCH_MMU_FLAG_PERM_USER)
        *flags |= MMU_FLAG_PERM_USER_WRITE;

    return TEE_SUCCESS;
}

/**
 * 检查是否为TA私有内存
 */
static bool is_ta_private_memory(vaddr_t va)
{
    /* 检查地址是否在TA私有内存区域 */
    if (va >= TA_PRIVATE_MEM_BASE && va < TA_PRIVATE_MEM_END)
        return true;

    /* 检查是否为TA分配的堆内存 */
    if (is_ta_heap_memory(va))
        return true;

    return false;
}
```

## 5. 标志定义

### 5.1 GP标志定义 (include/tee_api_defines.h)

```c
/* GP TEE内存访问标志 */
#define TEE_MEMORY_ACCESS_READ      0x00000001
#define TEE_MEMORY_ACCESS_WRITE     0x00000002
#define TEE_MEMORY_ACCESS_ANY_OWNER 0x00000004

/* Trusty内部MMU标志 */
#define MMU_FLAG_PERM_USER_READ     0x00000001
#define MMU_FLAG_PERM_USER_WRITE    0x00000002
```

## 6. 堆内存检查实现

### 6.1 Trusty堆内存检查函数 (user/base/lib/libutee/tee_api.c)

```c
/**
 * 检查缓冲区是否与堆内存重叠
 * 基于Trusty的brk系统调用和dlmalloc实现
 */
static bool trusty_buffer_overlaps_heap(void *buffer, size_t size)
{
    // 获取当前brk位置作为堆的当前结束地址
    void *current_brk = _rctee_brk(NULL);
    if (!current_brk) {
        return false;  // 堆未初始化
    }

    // 使用dlmalloc的footprint函数获取堆的实际使用情况
    size_t heap_footprint = dlmalloc_footprint();
    if (heap_footprint == 0) {
        return false;  // 堆未使用
    }

    // 估算堆的起始地址（这是一个近似值）
    // 在Trusty中，堆通常从一个固定的基地址开始
    uintptr_t heap_start = (uintptr_t)current_brk - heap_footprint;
    uintptr_t heap_end = (uintptr_t)current_brk;
    uintptr_t buf_start = (uintptr_t)buffer;
    uintptr_t buf_end = buf_start + size;

    // 检查缓冲区是否与堆区域重叠
    if (buf_start < heap_end && buf_end > heap_start) {
        return true;
    }

    return false;
}

/**
 * 检查缓冲区是否在已分配的堆内存范围内
 * 使用Trusty的dlmalloc_usable_size函数验证
 */
static bool trusty_buffer_is_within_allocated(void *buffer, size_t size)
{
    // 检查buffer是否为有效的malloc指针
    size_t usable_size = dlmalloc_usable_size(buffer);
    if (usable_size == 0) {
        return false;  // 不是有效的malloc指针
    }

    // 检查请求的size是否在可用大小范围内
    if (size <= usable_size) {
        return true;
    }

    return false;
}

/**
 * 检查缓冲区是否完全在已分配的堆块内
 * 更严格的检查：确保整个缓冲区都在同一个已分配的堆块内
 */
static bool trusty_buffer_fully_within_allocated(void *buffer, size_t size)
{
    if (!buffer || size == 0) {
        return false;
    }

    // 获取buffer指向的堆块的可用大小
    size_t usable_size = dlmalloc_usable_size(buffer);
    if (usable_size == 0) {
        return false;  // 不是有效的malloc指针
    }

    // 检查整个缓冲区是否都在这个堆块内
    uintptr_t buf_start = (uintptr_t)buffer;
    uintptr_t buf_end = buf_start + size;
    uintptr_t block_start = (uintptr_t)buffer;
    uintptr_t block_end = block_start + usable_size;

    // 确保缓冲区完全在已分配的堆块范围内
    return (buf_start >= block_start && buf_end <= block_end);
}
```

### 6.2 OP-TEE vs Trusty 堆检查对比

| 功能 | OP-TEE实现 | Trusty适配实现 |
|------|------------|----------------|
| 堆重叠检查 | `malloc_buffer_overlaps_heap()` | `trusty_buffer_overlaps_heap()` |
| 已分配检查 | `malloc_buffer_is_within_alloced()` | `trusty_buffer_is_within_allocated()` |
| 堆管理器 | 自定义malloc | dlmalloc |
| 堆边界获取 | 运行时查询 | brk系统调用 + dlmalloc_footprint |
| 分配验证 | 内部分配表 | `dlmalloc_usable_size()` |

### 6.3 堆内存检查集成

在TEE_CheckMemoryAccessRights的第三步中集成：

```c
/*
 * 第三步：检查堆内存重叠（如果适用）
 * 防止TA暴露私有堆内存
 */
if (trusty_buffer_overlaps_heap(buffer, size) &&
    !trusty_buffer_is_within_allocated(buffer, size))
    return TEE_ERROR_ACCESS_DENIED;
```

## 7. 实现要点

### 7.1 安全保证
1. **严格用户/内核分离**：用户空间只能通过系统调用访问内核功能
2. **完整性检查**：确保整个缓冲区都满足访问权限要求
3. **所有者验证**：支持TEE_MEMORY_ACCESS_ANY_OWNER标志的语义
4. **参数验证**：防止地址溢出和无效参数
5. **堆内存保护**：防止TA暴露未分配的堆内存区域

### 7.2 GP标准兼容
1. **完整标志支持**：READ、WRITE、ANY_OWNER标志
2. **正确返回码**：TEE_SUCCESS、TEE_ERROR_ACCESS_DENIED
3. **边界条件处理**：size=0返回成功
4. **缓冲区行为**：不访问缓冲区内容，仅检查权限

### 6.3 关键技术点
1. **系统调用封装**：通过_rctee_check_memory_access_rights封装内核功能
2. **权限转换**：Trusty MMU标志与GP TEE标志的正确转换
3. **内存分类**：区分TA私有内存、共享内存、堆内存
4. **逐页检查**：按页粒度检查内存权限

## 7. 集成步骤

1. **添加系统调用定义**：在kernel/rctee/lib/rctee/include/syscall_table.h中添加DEF_SYSCALL
2. **实现系统调用处理**：在kernel/rctee/lib/rctee/rctee_core/syscall.c中实现sys_check_memory_access_rights
3. **实现内核检查函数**：添加trusty_memory_check.c文件
4. **更新用户空间**：在lib/libutee/tee_api.c中实现TEE_CheckMemoryAccessRights
5. **定义标志常量**：在头文件中添加必要的宏定义
6. **重新构建**：运行./local_build.sh重新生成系统调用存根
7. **测试验证**：编写测试用例验证各种场景

## 8. Trusty系统调用机制说明

### 8.1 自动代码生成
- Trusty使用stubgen.py工具自动生成用户空间系统调用函数
- 在syscall_table.h中定义DEF_SYSCALL后，构建系统会自动生成：
  - 用户空间函数：_rctee_check_memory_access_rights()
  - 汇编存根：rctee_syscalls.S
  - 头文件：rctee_syscalls.h

### 8.2 命名规范
- **系统调用定义**：使用DEF_SYSCALL(nr, name, ret_type, arg_count, ...)
- **用户空间函数**：自动生成为_rctee_<name>()格式
- **内核处理函数**：手动实现为sys_<name>()格式

## 9. 使用示例

### 9.1 基本权限检查

```c
// 检查读权限
if (TEE_CheckMemoryAccessRights(TEE_MEMORY_ACCESS_READ, buffer, size) != TEE_SUCCESS) {
    return TEE_ERROR_ACCESS_DENIED;
}

// 检查读写权限
if (TEE_CheckMemoryAccessRights(TEE_MEMORY_ACCESS_READ | TEE_MEMORY_ACCESS_WRITE,
                                buffer, size) != TEE_SUCCESS) {
    return TEE_ERROR_ACCESS_DENIED;
}

// 检查任意所有者的读权限
if (TEE_CheckMemoryAccessRights(TEE_MEMORY_ACCESS_READ | TEE_MEMORY_ACCESS_ANY_OWNER,
                                buffer, size) != TEE_SUCCESS) {
    return TEE_ERROR_ACCESS_DENIED;
}
```

### 9.2 堆内存检查示例

```c
// 堆内存分配和检查
void *heap_buffer = malloc(1024);
if (heap_buffer) {
    // 检查已分配的堆内存 - 应该成功
    if (TEE_CheckMemoryAccessRights(TEE_MEMORY_ACCESS_WRITE, heap_buffer, 1024) != TEE_SUCCESS) {
        // 不应该到达这里
        free(heap_buffer);
        return TEE_ERROR_ACCESS_DENIED;
    }

    // 检查超出分配大小的访问 - 应该失败
    if (TEE_CheckMemoryAccessRights(TEE_MEMORY_ACCESS_WRITE, heap_buffer, 2048) == TEE_SUCCESS) {
        // 这表明检查有问题，因为访问超出了分配的大小
    }

    free(heap_buffer);
}

// 检查未分配的堆区域 - 应该失败
char *unallocated_heap_ptr = (char*)heap_buffer + 1024;  // 指向堆区域但未分配
if (TEE_CheckMemoryAccessRights(TEE_MEMORY_ACCESS_READ, unallocated_heap_ptr, 100) == TEE_SUCCESS) {
    // 这表明检查有问题，因为访问了未分配的堆内存
}
```

### 9.3 边界条件测试

```c
// 零大小检查 - 应该总是成功
if (TEE_CheckMemoryAccessRights(TEE_MEMORY_ACCESS_READ, buffer, 0) != TEE_SUCCESS) {
    // 不应该到达这里
}

// NULL指针检查 - 应该失败
if (TEE_CheckMemoryAccessRights(TEE_MEMORY_ACCESS_READ, NULL, 100) == TEE_SUCCESS) {
    // 这表明检查有问题
}
```
- **系统调用号**：使用十六进制，按顺序递增

这个方案完全符合Trusty的系统调用架构和命名规范，确保了与现有代码的一致性。
```
