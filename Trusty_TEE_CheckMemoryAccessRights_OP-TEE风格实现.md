# Trusty TEE_CheckMemoryAccessRights OP-TEE风格分层实现

## 1. 设计原则

### 1.1 OP-TEE分层策略
- **用户层优先**：能在用户空间完成的检查都在用户空间做
- **内核层必要**：只有需要特权访问的检查才通过系统调用
- **性能优化**：最小化系统调用开销
- **分配器感知**：用户层可以直接调用分配器函数

### 1.2 检查分层划分

#### 用户层检查（user/base/lib/libutee/tee_api.c）
1. **基础参数检查**：size=0, NULL指针, 标志有效性
2. **堆内存安全检查**：堆重叠检查, 已分配验证
3. **TA参数兼容性检查**：与TA参数的权限兼容性
4. **分配器特定检查**：dlmalloc/scudo特定的验证

#### 内核层检查（通过系统调用）
1. **MMU权限查询**：arch_mmu_query()
2. **内存映射验证**：VMM状态检查
3. **地址空间边界**：用户空间范围验证
4. **特权级别验证**：内存所有权和信任级别

## 2. 用户层完整实现

### 2.1 主函数实现
```c
// user/base/lib/libutee/tee_api.c
#include <tee_internal_api.h>
#include <rctee_syscalls.h>
#include <lib/dlmalloc.h>
#include <lib/scudo.h>

/**
 * GP标准TEE_CheckMemoryAccessRights - OP-TEE风格分层实现
 */
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size)
{
    uint32_t flags = accessFlags;
    TEE_Result res;

    /* 第一步：用户层基础检查 */
    res = user_basic_parameter_checks(accessFlags, buffer, size);
    if (res != TEE_SUCCESS)
        return res;

    /* 第二步：用户层堆内存安全检查 */
    res = user_heap_safety_checks(buffer, size);
    if (res != TEE_SUCCESS)
        return res;

    /* 第三步：用户层TA参数兼容性检查 */
    flags &= TEE_MEMORY_ACCESS_READ | TEE_MEMORY_ACCESS_WRITE | TEE_MEMORY_ACCESS_ANY_OWNER;
    res = user_check_mem_access_rights_params(flags, buffer, size);
    if (res != TEE_SUCCESS)
        return res;

    /* 第四步：内核层MMU权限检查（通过系统调用） */
    long result = _rctee_check_memory_access_rights(accessFlags, buffer, size);
    return (TEE_Result)result;
}
```

### 2.2 用户层基础检查
```c
/**
 * 用户层基础参数检查 - 像OP-TEE一样
 */
static TEE_Result user_basic_parameter_checks(uint32_t accessFlags, void *buffer, size_t size)
{
    /* GP标准：size为0时直接返回成功 */
    if (!size)
        return TEE_SUCCESS;

    /* GP标准：NULL指针检查 */
    if (!buffer)
        return TEE_ERROR_BAD_PARAMETERS;

    /* GP标准：访问标志有效性检查 */
    if (!(accessFlags & (TEE_MEMORY_ACCESS_READ | TEE_MEMORY_ACCESS_WRITE))) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* GP标准：保留标志检查 */
    uint32_t valid_flags = TEE_MEMORY_ACCESS_READ | TEE_MEMORY_ACCESS_WRITE | 
                          TEE_MEMORY_ACCESS_ANY_OWNER;
    if (accessFlags & ~valid_flags) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 地址溢出检查 */
    uintptr_t buf_addr = (uintptr_t)buffer;
    if (buf_addr + size < buf_addr) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    return TEE_SUCCESS;
}
```

### 2.3 用户层堆内存安全检查
```c
/**
 * 用户层堆内存安全检查 - 分配器感知
 */
static TEE_Result user_heap_safety_checks(void *buffer, size_t size)
{
    /* 检查是否与堆内存重叠 */
    if (user_buffer_overlaps_heap(buffer, size)) {
        /* 如果与堆重叠，必须在已分配的堆块内 */
        if (!user_buffer_is_within_allocated(buffer, size)) {
            return TEE_ERROR_ACCESS_DENIED;
        }
    }

    return TEE_SUCCESS;
}

/**
 * 检查缓冲区是否与堆内存重叠 - 分配器感知
 */
static bool user_buffer_overlaps_heap(void *buffer, size_t size)
{
    /* 获取当前brk位置 */
    void *current_brk = _rctee_brk(NULL);
    if (!current_brk)
        return false;

    /* 根据编译时配置选择分配器 */
#ifdef RCTEE_TA_ALLOCATOR_SCUDO
    /* 使用scudo获取堆信息 */
    size_t heap_footprint = scudo_get_heap_size();
#else
    /* 默认使用dlmalloc */
    size_t heap_footprint = dlmalloc_footprint();
#endif

    if (heap_footprint == 0)
        return false;

    /* 计算堆边界 */
    uintptr_t heap_start = (uintptr_t)current_brk - heap_footprint;
    uintptr_t heap_end = (uintptr_t)current_brk;
    uintptr_t buf_start = (uintptr_t)buffer;
    uintptr_t buf_end = buf_start + size;

    return (buf_start < heap_end && buf_end > heap_start);
}

/**
 * 检查缓冲区是否在已分配的堆块内 - 分配器感知
 */
static bool user_buffer_is_within_allocated(void *buffer, size_t size)
{
    /* 根据编译时配置选择分配器 */
#ifdef RCTEE_TA_ALLOCATOR_SCUDO
    /* 使用scudo验证分配 */
    size_t usable_size = SCUDO_PREFIX(malloc_usable_size)(buffer);
#else
    /* 默认使用dlmalloc */
    size_t usable_size = dlmalloc_usable_size(buffer);
#endif

    if (usable_size == 0)
        return false;

    /* 检查整个缓冲区是否在分配块内 */
    return (size <= usable_size);
}
```

### 2.4 用户层TA参数兼容性检查
```c
/**
 * 用户层TA参数兼容性检查 - 像OP-TEE的check_mem_access_rights_params
 */
static TEE_Result user_check_mem_access_rights_params(uint32_t flags, void *buffer, size_t size)
{
    /*
     * 检查与当前TA参数的兼容性
     * 这里实现与OP-TEE类似的参数检查逻辑
     */
    
    /* 获取当前TA的参数信息 */
    struct ta_session_info *session = get_current_ta_session();
    if (!session)
        return TEE_ERROR_BAD_STATE;

    /* 检查缓冲区是否与TA参数冲突 */
    for (uint32_t i = 0; i < session->param_count; i++) {
        if (session->params[i].memref.buffer == buffer) {
            /* 检查访问权限是否兼容 */
            if (!check_param_access_compatibility(flags, &session->params[i])) {
                return TEE_ERROR_ACCESS_DENIED;
            }
        }
    }

    return TEE_SUCCESS;
}
```

## 3. 内核层精简实现

### 3.1 系统调用定义
```c
// kernel/rctee/lib/rctee/include/syscall_table.h
DEF_SYSCALL(0x42, check_memory_access_rights, long, 3, uint32_t flags, const void *buf, size_t len)
```

### 3.2 内核层MMU权限检查
```c
// kernel/rctee/lib/rctee/rctee_core/syscall.c

/**
 * 内核层MMU权限检查 - 只做用户层无法完成的检查
 */
static long sys_check_memory_access_rights(uint32_t flags, const void *buf, size_t len)
{
    vaddr_t buf_addr = (vaddr_t)buf;
    struct rctee_app* app = current_rctee_app();

    /* 基础参数验证（防御性编程） */
    if (!len)
        return TEE_SUCCESS;
    if (!buf)
        return TEE_ERROR_BAD_PARAMETERS;

    /* 检查地址是否在用户空间范围内 */
    if (!is_user_address_range(buf_addr, len))
        return TEE_ERROR_ACCESS_DENIED;

    /* MMU权限检查 - 这是内核层的核心工作 */
    TEE_Result res = kernel_check_mmu_permissions(flags, buf_addr, len);
    if (res != TEE_SUCCESS)
        return res;

    /* 内存映射验证 */
    res = kernel_check_memory_mapping(app, buf_addr, len);
    if (res != TEE_SUCCESS)
        return res;

    return TEE_SUCCESS;
}
```

## 4. 优势分析

### 4.1 性能优势
- **减少系统调用**：大部分检查在用户层完成
- **分配器直接访问**：用户层可以直接调用分配器函数
- **缓存友好**：用户层检查利用CPU缓存

### 4.2 架构优势
- **清晰分层**：用户层和内核层职责明确
- **OP-TEE兼容**：与OP-TEE的设计模式一致
- **易于维护**：分层设计便于调试和维护

### 4.3 安全优势
- **深度防御**：用户层和内核层双重检查
- **特权分离**：内核层只处理需要特权的检查
- **GP标准兼容**：完全符合GP标准要求

## 5. 编译时分配器检测

### 5.1 分配器配置检测
```c
// user/base/lib/libutee/tee_api_allocator.h
#ifndef TEE_API_ALLOCATOR_H
#define TEE_API_ALLOCATOR_H

/* 根据make/compability.mk的配置检测分配器 */
#ifdef RCTEE_TA_ALLOCATOR_SCUDO
    #define TRUSTY_ALLOCATOR_SCUDO 1
    #include <lib/scudo.h>
#else
    #define TRUSTY_ALLOCATOR_DLMALLOC 1
    #include <lib/dlmalloc.h>
#endif

/* 分配器无关的接口封装 */
static inline size_t trusty_malloc_usable_size(void *ptr)
{
#ifdef TRUSTY_ALLOCATOR_SCUDO
    return SCUDO_PREFIX(malloc_usable_size)(ptr);
#else
    return dlmalloc_usable_size(ptr);
#endif
}

static inline size_t trusty_heap_footprint(void)
{
#ifdef TRUSTY_ALLOCATOR_SCUDO
    return scudo_get_heap_size();  /* 需要实现 */
#else
    return dlmalloc_footprint();
#endif
}

#endif /* TEE_API_ALLOCATOR_H */
```

### 5.2 优化的堆检查实现
```c
/**
 * 优化的堆重叠检查 - 使用分配器无关接口
 */
static bool user_buffer_overlaps_heap(void *buffer, size_t size)
{
    /* 获取当前brk位置 */
    void *current_brk = _rctee_brk(NULL);
    if (!current_brk)
        return false;

    /* 使用分配器无关接口 */
    size_t heap_footprint = trusty_heap_footprint();
    if (heap_footprint == 0)
        return false;

    /* 计算堆边界 */
    uintptr_t heap_start = (uintptr_t)current_brk - heap_footprint;
    uintptr_t heap_end = (uintptr_t)current_brk;
    uintptr_t buf_start = (uintptr_t)buffer;
    uintptr_t buf_end = buf_start + size;

    return (buf_start < heap_end && buf_end > heap_start);
}

/**
 * 优化的已分配检查 - 使用分配器无关接口
 */
static bool user_buffer_is_within_allocated(void *buffer, size_t size)
{
    /* 使用分配器无关接口 */
    size_t usable_size = trusty_malloc_usable_size(buffer);
    if (usable_size == 0)
        return false;

    /* 检查整个缓冲区是否在分配块内 */
    return (size <= usable_size);
}
```

## 6. 构建系统集成

### 6.1 Makefile集成
```makefile
# user/base/lib/libutee/rules.mk

# 根据分配器配置添加相应的源文件
ifeq ($(RCTEE_TA_ALLOCATOR),scudo)
    MODULE_CFLAGS += -DRCTEE_TA_ALLOCATOR_SCUDO
    MODULE_DEPS += user/base/lib/scudo
else
    MODULE_CFLAGS += -DRCTEE_TA_ALLOCATOR_DLMALLOC
    MODULE_DEPS += user/base/lib/dlmalloc
endif

MODULE_SRCS += \
    tee_api.c \
    tee_api_allocator.c \
```

### 6.2 头文件依赖
```c
// user/base/lib/libutee/tee_api.c
#include <tee_internal_api.h>
#include <rctee_syscalls.h>
#include "tee_api_allocator.h"  /* 分配器无关接口 */

/* 不再直接包含分配器特定头文件 */
/* #include <lib/dlmalloc.h>  -- 移除 */
/* #include <lib/scudo.h>     -- 移除 */
```

## 7. 测试和验证

### 7.1 分配器兼容性测试
```c
// 测试用例：验证两种分配器配置
void test_allocator_compatibility(void)
{
    void *ptr = TEE_Malloc(1024);

    /* 测试分配器无关接口 */
    size_t usable = trusty_malloc_usable_size(ptr);
    assert(usable >= 1024);

    /* 测试堆检查功能 */
    TEE_Result res = TEE_CheckMemoryAccessRights(
        TEE_MEMORY_ACCESS_READ | TEE_MEMORY_ACCESS_WRITE,
        ptr, 1024);
    assert(res == TEE_SUCCESS);

    TEE_Free(ptr);
}
```

### 7.2 性能基准测试
```c
// 性能测试：对比用户层vs内核层检查开销
void benchmark_check_performance(void)
{
    void *ptr = TEE_Malloc(4096);

    /* 测试用户层检查性能 */
    uint64_t start = get_time_us();
    for (int i = 0; i < 10000; i++) {
        user_heap_safety_checks(ptr, 4096);
    }
    uint64_t user_time = get_time_us() - start;

    /* 测试完整检查性能 */
    start = get_time_us();
    for (int i = 0; i < 10000; i++) {
        TEE_CheckMemoryAccessRights(
            TEE_MEMORY_ACCESS_READ | TEE_MEMORY_ACCESS_WRITE,
            ptr, 4096);
    }
    uint64_t full_time = get_time_us() - start;

    printf("User layer: %llu us, Full check: %llu us\n",
           user_time, full_time);

    TEE_Free(ptr);
}
```

## 8. 总结

### 8.1 实现特点
- **OP-TEE风格分层**：用户层优先，内核层必要
- **分配器无关**：编译时适配dlmalloc和scudo
- **性能优化**：最小化系统调用开销
- **GP标准兼容**：完整实现所有GP要求

### 8.2 架构优势
- **清晰职责分离**：用户层处理应用逻辑，内核层处理特权操作
- **易于维护**：分层设计便于调试和扩展
- **高性能**：大部分检查在用户层完成，减少上下文切换

这个OP-TEE风格的分层实现既保持了性能，又确保了安全性和GP标准兼容性。
