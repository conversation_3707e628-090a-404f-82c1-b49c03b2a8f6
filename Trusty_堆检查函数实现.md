# Trusty 堆检查函数实现

## 核心修改：替换堆检查部分

```c
// 原OP-TEE代码：
if (malloc_buffer_overlaps_heap(buffer, size) &&
    !malloc_buffer_is_within_allocated(buffer, size))
    return TEE_ERROR_ACCESS_DENIED;

// 替换为：
if (trusty_buffer_overlaps_heap(buffer, size) &&
    !trusty_buffer_is_within_allocated(buffer, size))
    return TEE_ERROR_ACCESS_DENIED;
```

## 2. trusty_buffer_overlaps_heap() 实现

### 2.1 分配器感知实现
```c
/**
 * 检查缓冲区是否与堆内存重叠 - Trusty分配器感知
 * 替换OP-TEE的malloc_buffer_overlaps_heap()
 */
static bool trusty_buffer_overlaps_heap(void *buffer, size_t size)
{
    if (!buffer || !size)
        return false;

    /* 根据编译时分配器配置选择实现 */
#ifdef RCTEE_TA_ALLOCATOR_SCUDO
    return scudo_buffer_overlaps_heap(buffer, size);
#else
    return dlmalloc_buffer_overlaps_heap(buffer, size);
#endif
}

/**
 * dlmalloc版本的堆重叠检查
 */
static bool dlmalloc_buffer_overlaps_heap(void *buffer, size_t size)
{
    /* 获取dlmalloc堆信息 - 用户层接口 */
    void *heap_base = dlmalloc_get_heap_base();
    size_t heap_size = dlmalloc_footprint();
    
    if (!heap_base || heap_size == 0)
        return false;

    /* 计算重叠 */
    uintptr_t heap_start = (uintptr_t)heap_base;
    uintptr_t heap_end = heap_start + heap_size;
    uintptr_t buf_start = (uintptr_t)buffer;
    uintptr_t buf_end = buf_start + size;

    return (buf_start < heap_end && buf_end > heap_start);
}

/**
 * scudo版本的堆重叠检查
 */
static bool scudo_buffer_overlaps_heap(void *buffer, size_t size)
{
    /* 获取scudo堆信息 - 用户层接口 */
    void *heap_base = scudo_get_heap_base();
    size_t heap_size = scudo_get_heap_size();
    
    if (!heap_base || heap_size == 0)
        return false;

    /* 计算重叠 */
    uintptr_t heap_start = (uintptr_t)heap_base;
    uintptr_t heap_end = heap_start + heap_size;
    uintptr_t buf_start = (uintptr_t)buffer;
    uintptr_t buf_end = buf_start + size;

    return (buf_start < heap_end && buf_end > heap_start);
}
```

## 3. trusty_buffer_is_within_allocated() 实现

### 3.1 分配器感知实现
```c
/**
 * 检查缓冲区是否在已分配的堆块内 - Trusty分配器感知
 * 替换OP-TEE的malloc_buffer_is_within_allocated()
 */
static bool trusty_buffer_is_within_allocated(void *buffer, size_t size)
{
    if (!buffer || !size)
        return false;

    /* 根据编译时分配器配置选择实现 */
#ifdef RCTEE_TA_ALLOCATOR_SCUDO
    return scudo_buffer_is_within_allocated(buffer, size);
#else
    return dlmalloc_buffer_is_within_allocated(buffer, size);
#endif
}

/**
 * dlmalloc版本的已分配检查
 */
static bool dlmalloc_buffer_is_within_allocated(void *buffer, size_t size)
{
    /* 使用dlmalloc的用户层接口检查 */
    size_t usable_size = dlmalloc_usable_size(buffer);
    
    /* 如果usable_size为0，说明不是有效的已分配块 */
    if (usable_size == 0)
        return false;

    /* 检查整个缓冲区是否在已分配块内 */
    return (size <= usable_size);
}

/**
 * scudo版本的已分配检查
 */
static bool scudo_buffer_is_within_allocated(void *buffer, size_t size)
{
    /* 使用scudo的用户层接口检查 */
    size_t usable_size = SCUDO_PREFIX(malloc_usable_size)(buffer);
    
    /* 如果usable_size为0，说明不是有效的已分配块 */
    if (usable_size == 0)
        return false;

    /* 检查整个缓冲区是否在已分配块内 */
    return (size <= usable_size);
}
```

## 4. 需要的分配器接口扩展

### 4.1 dlmalloc需要的接口
```c
/**
 * dlmalloc需要添加的用户层接口
 * 位置：user/base/lib/dlmalloc/dlmalloc.c
 */

/* 获取堆基地址 - 需要添加 */
void *dlmalloc_get_heap_base(void)
{
    /* 返回dlmalloc内部的堆基地址 */
    extern void *dlmalloc_heap_start;  /* 需要暴露的内部变量 */
    return dlmalloc_heap_start;
}

/* dlmalloc_usable_size() - 已存在 */
/* dlmalloc_footprint() - 已存在 */
```

### 4.2 scudo需要的接口
```c
/**
 * scudo需要添加的用户层接口
 * 位置：user/base/lib/scudo/scudo.c
 */

/* 获取堆基地址 - 需要添加 */
void *scudo_get_heap_base(void)
{
    /* 返回scudo内部的堆基地址 */
    extern void *scudo_heap_start;  /* 需要暴露的内部变量 */
    return scudo_heap_start;
}

/* 获取堆大小 - 需要添加 */
size_t scudo_get_heap_size(void)
{
    /* 返回scudo内部的堆大小 */
    extern size_t scudo_heap_size;  /* 需要暴露的内部变量 */
    return scudo_heap_size;
}

/* SCUDO_PREFIX(malloc_usable_size)() - 已存在 */
```

## 5. 头文件包含

### 5.1 条件包含
```c
// user/base/lib/libutee/tee_api.c

/* 根据分配器配置包含相应头文件 */
#ifdef RCTEE_TA_ALLOCATOR_SCUDO
    #include <lib/scudo.h>
    /* scudo特定的宏和函数 */
#else
    #include <lib/dlmalloc.h>
    /* dlmalloc特定的函数 */
#endif
```

## 6. 编译配置

### 6.1 Makefile集成
```makefile
# user/base/lib/libutee/rules.mk

# 根据分配器配置添加编译标志
ifeq ($(RCTEE_TA_ALLOCATOR),scudo)
    MODULE_CFLAGS += -DRCTEE_TA_ALLOCATOR_SCUDO
    MODULE_DEPS += user/base/lib/scudo
else
    MODULE_CFLAGS += -DRCTEE_TA_ALLOCATOR_DLMALLOC
    MODULE_DEPS += user/base/lib/dlmalloc
endif
```

## 7. 使用示例

### 7.1 在TEE_CheckMemoryAccessRights中使用
```c
// user/base/lib/libutee/tee_api.c

TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size)
{
    /* ... 其他检查 ... */

    /*
     * 第三步：检查堆内存重叠（如果适用）
     * 防止TA暴露私有堆内存 - 支持Trusty分配器
     */
    if (trusty_buffer_overlaps_heap(buffer, size) &&
        !trusty_buffer_is_within_allocated(buffer, size))
        return TEE_ERROR_ACCESS_DENIED;

    /* ... 继续其他检查 ... */
}
```

## 8. 测试验证

### 8.1 功能测试
```c
void test_trusty_heap_checks(void)
{
    /* 测试已分配内存 */
    void *ptr = TEE_Malloc(1024);
    assert(trusty_buffer_overlaps_heap(ptr, 1024) == true);
    assert(trusty_buffer_is_within_allocated(ptr, 1024) == true);
    
    /* 测试超出边界 */
    assert(trusty_buffer_is_within_allocated(ptr, 2048) == false);
    
    /* 测试未分配内存 */
    void *invalid_ptr = (char*)ptr + 2048;
    assert(trusty_buffer_is_within_allocated(invalid_ptr, 100) == false);
    
    TEE_Free(ptr);
}
```

### 8.2 分配器兼容性测试
```c
void test_allocator_compatibility(void)
{
    /* 测试在两种分配器配置下都能正常工作 */
    void *ptr = TEE_Malloc(512);
    
    /* 这些调用应该在dlmalloc和scudo下都能工作 */
    bool overlaps = trusty_buffer_overlaps_heap(ptr, 512);
    bool within = trusty_buffer_is_within_allocated(ptr, 512);
    
    assert(overlaps == true);
    assert(within == true);
    
    TEE_Free(ptr);
}
```

这个实现只替换了您指定的堆检查部分，保持了与OP-TEE的兼容性，同时支持Trusty的两种分配器配置。
