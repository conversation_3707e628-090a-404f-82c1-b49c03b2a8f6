# TEE_CheckMemoryAccessRights 系统调用扩展方案

## 1. 方案概述

通过在trusty中增加新的系统调用，使用户空间能够查询内存权限，从而达到与OP-TEE完全一样的效果，严格符合GP标准要求。

## 2. 新增系统调用设计

### 2.1 核心系统调用

**系统调用定义**：
```c
// 系统调用号：0x42 (在现有系统调用表后添加)
DEF_SYSCALL(0x42, _rctee_check_memory_access_rights, long, 3, void *buffer, uint32_t size, uint32_t access_flags)
```

**用户空间接口**：
```c
// user/base/lib/libc-rctee/include/rctee_syscalls.h
long _rctee_check_memory_access_rights(void *buffer, uint32_t size, uint32_t access_flags);

// user/base/lib/libc-rctee/memory_check.c
int check_memory_access_rights(void *buffer, size_t size, uint32_t access_flags) {
    if (size > UINT32_MAX) {
        return ERR_INVALID_ARGS;
    }
    return _rctee_check_memory_access_rights(buffer, (uint32_t)size, access_flags);
}
```



## 3. 内核实现方案

### 3.1 系统调用实现

```c
// kernel/rctee/lib/rctee/rctee_core/syscall.c
long sys__rctee_check_memory_access_rights(user_addr_t buffer, uint32_t size, uint32_t access_flags) {
    struct rctee_app* rctee_app = current_rctee_app();
    vaddr_t vaddr = (vaddr_t)buffer;
    
    // 基本参数检查
    if (!size) return NO_ERROR;  // GP标准：零大小返回成功
    if (!buffer) return ERR_ACCESS_DENIED;  // NULL指针拒绝
    
    // 地址溢出检查
    if (vaddr + size < vaddr) return ERR_ACCESS_DENIED;
    
    // 检查地址是否在用户空间范围内
    if (!is_user_address(vaddr) || !is_user_address(vaddr + size - 1))
        return ERR_ACCESS_DENIED;

    // GP标准：识别内存类型并验证最小权限要求
    memory_type_t mem_type = identify_memory_type(rctee_app, vaddr, size);
    if (!check_minimum_access_rights(mem_type, access_flags)) {
        return ERR_ACCESS_DENIED;
    }

    // 按页面检查MMU权限
    status_t ret = check_memory_pages_access_rights(rctee_app->aspace, vaddr, size, access_flags);
    if (ret != NO_ERROR) {
        return ret;
    }

    // GP标准：对共享内存进行特殊的一致性检查
    if (mem_type == MEMORY_TYPE_SHARED_CA &&
        !(access_flags & TEE_MEMORY_ACCESS_ANY_OWNER)) {
        // 共享内存不满足一致性保证，必须设置ANY_OWNER标志
        return ERR_ACCESS_DENIED;
    }

    return NO_ERROR;
}

// 按页面检查内存访问权限（类似OP-TEE的实现）
static status_t check_memory_pages_access_rights(vmm_aspace_t *aspace, vaddr_t vaddr,
                                                uint32_t size, uint32_t access_flags) {
    size_t offset = vaddr & (PAGE_SIZE - 1);
    size_t aligned_size = round_up(size + offset, PAGE_SIZE);
    vaddr_t aligned_vaddr = round_down(vaddr, PAGE_SIZE);

    while (aligned_size > 0) {
        uint arch_mmu_flags;
        paddr_t paddr;

        // 查询页面的MMU权限
        status_t ret = arch_mmu_query(&aspace->arch_aspace, aligned_vaddr, &paddr, &arch_mmu_flags);
        if (ret != NO_ERROR) {
            return ERR_ACCESS_DENIED;  // 页面未映射
        }

        // 检查读权限
        if ((access_flags & TEE_MEMORY_ACCESS_READ) &&
            !(arch_mmu_flags & ARCH_MMU_FLAG_PERM_USER)) {
            return ERR_ACCESS_DENIED;
        }

        // 检查写权限
        if ((access_flags & TEE_MEMORY_ACCESS_WRITE) &&
            (arch_mmu_flags & ARCH_MMU_FLAG_PERM_RO)) {
            return ERR_ACCESS_DENIED;
        }

        aligned_vaddr += PAGE_SIZE;
        aligned_size -= PAGE_SIZE;
    }

    return NO_ERROR;
}

// GP标准：内存类型定义
typedef enum {
    MEMORY_TYPE_TA_HEAP = 1,      // TA堆内存
    MEMORY_TYPE_TA_STACK = 2,     // TA栈内存
    MEMORY_TYPE_TA_DATA = 3,      // TA数据段
    MEMORY_TYPE_TA_CONST = 4,     // TA常量段
    MEMORY_TYPE_TA_CODE = 5,      // TA代码段
    MEMORY_TYPE_TEE_PARAM = 6,    // TEE参数结构
    MEMORY_TYPE_SHARED_CA = 7,    // CA共享内存
    MEMORY_TYPE_UNKNOWN = 0       // 未知类型
} memory_type_t;

// GP标准：识别内存类型
static memory_type_t identify_memory_type(struct rctee_app *rctee_app, vaddr_t vaddr, size_t size) {
    // 检查是否为堆内存
    if (is_heap_memory(rctee_app, vaddr, size)) {
        return MEMORY_TYPE_TA_HEAP;
    }

    // 检查是否为栈内存
    if (is_stack_memory(rctee_app, vaddr, size)) {
        return MEMORY_TYPE_TA_STACK;
    }

    // 检查是否为数据段
    if (is_data_segment(rctee_app, vaddr, size)) {
        return MEMORY_TYPE_TA_DATA;
    }

    // 检查是否为常量段
    if (is_const_segment(rctee_app, vaddr, size)) {
        return MEMORY_TYPE_TA_CONST;
    }

    // 检查是否为代码段
    if (is_code_segment(rctee_app, vaddr, size)) {
        return MEMORY_TYPE_TA_CODE;
    }

    // 检查是否为TEE参数
    if (is_tee_param_memory(rctee_app, vaddr, size)) {
        return MEMORY_TYPE_TEE_PARAM;
    }

    // 检查是否为共享内存
    if (is_shared_memory(rctee_app, vaddr, size)) {
        return MEMORY_TYPE_SHARED_CA;
    }

    return MEMORY_TYPE_UNKNOWN;
}

// GP标准：检查最小权限要求
static bool check_minimum_access_rights(memory_type_t mem_type, uint32_t access_flags) {
    switch (mem_type) {
    case MEMORY_TYPE_TA_HEAP:
    case MEMORY_TYPE_TA_STACK:
    case MEMORY_TYPE_TA_DATA:
    case MEMORY_TYPE_TEE_PARAM:
        // 这些内存必须由TA拥有，必须有读写权限
        // 如果请求访问但未设置ANY_OWNER，则必须满足TA拥有的要求
        return true;  // 系统调用层面的基本检查，详细检查在MMU层

    case MEMORY_TYPE_TA_CONST:
        // 常量段必须由TA拥有，必须有读权限，不应该有写权限
        if ((access_flags & TEE_MEMORY_ACCESS_WRITE) &&
            !(access_flags & TEE_MEMORY_ACCESS_ANY_OWNER)) {
            return false;  // 不允许写入常量段
        }
        return true;

    case MEMORY_TYPE_TA_CODE:
        // 代码段必须由TA拥有，可能有读权限，必须有执行权限
        // 通常不允许写入代码段
        if ((access_flags & TEE_MEMORY_ACCESS_WRITE) &&
            !(access_flags & TEE_MEMORY_ACCESS_ANY_OWNER)) {
            return false;  // 不允许写入代码段
        }
        return true;

    case MEMORY_TYPE_SHARED_CA:
        // 共享内存由CA拥有，权限需要和CA传过来的参数对应
        // 这里只做基本检查，详细检查在参数兼容性检查中进行
        return true;

    case MEMORY_TYPE_UNKNOWN:
    default:
        // 未知类型的内存，拒绝访问
        return false;
    }
}
```



## 4. 用户空间完整实现

### 4.1 TEE_CheckMemoryAccessRights完整实现

## 4. 用户空间实现

### 4.1 TEE_CheckMemoryAccessRights主函数
```c
// user/base/lib/libutee/tee_api.c

/**
 * GP标准TEE_CheckMemoryAccessRights实现
 */
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size)
{
    uint32_t flags = accessFlags;

    /* GP标准：size为0时直接返回成功 */
    if (!size)
        return TEE_SUCCESS;

    /* GP标准：NULL指针检查 - 必须拒绝NULL指针访问 */
    if (!buffer)
        return TEE_ERROR_ACCESS_DENIED;

    /* GP标准：地址溢出检查 - 防止整数溢出攻击 */
    if ((uintptr_t)buffer + size < (uintptr_t)buffer)
        return TEE_ERROR_ACCESS_DENIED;

    /* GP标准：标志位验证 - 只允许定义的标志位 */
    if (accessFlags & ~(TEE_MEMORY_ACCESS_READ | TEE_MEMORY_ACCESS_WRITE |
                       TEE_MEMORY_ACCESS_ANY_OWNER))
        return TEE_ERROR_BAD_PARAMETERS;

    /*
     * 第一步：通过系统调用检查内存映射权限
     * 这是核心的MMU权限检查，由内核完成
     * 系统调用内部会进行：
     * - MMU页面权限验证
     * - 内存类型识别（堆/栈/数据段/代码段/共享内存）
     * - 最小权限要求验证
     */
    if (_rctee_check_memory_access_rights(buffer, size, accessFlags))
        return TEE_ERROR_ACCESS_DENIED;

    /*
     * 第二步：检查与TA参数的权限兼容性
     * 清除扩展标志，只保留基本访问标志
     * 这确保共享内存的一致性保证
     */
    flags &= TEE_MEMORY_ACCESS_READ | TEE_MEMORY_ACCESS_WRITE |
             TEE_MEMORY_ACCESS_ANY_OWNER;
    if (check_mem_access_rights_params(flags, buffer, size))
        return TEE_ERROR_ACCESS_DENIED;

    /*
     * 第三步：检查堆内存重叠（如果适用）
     * 防止TA暴露私有堆内存 - 支持Trusty分配器
     * 这确保堆内存的安全性和一致性保证
     */
    if (trusty_buffer_overlaps_heap(buffer, size) &&
        !trusty_buffer_is_within_allocated(buffer, size))
        return TEE_ERROR_ACCESS_DENIED;

    return TEE_SUCCESS;
}
```

### 4.2 辅助函数实现
```c
/**
 * GP标准：检查内存访问权限的辅助函数 - 检查与TA参数的重叠
 * 这个函数确保共享内存的一致性保证
 */
static TEE_Result check_mem_access_rights_params(uint32_t flags, void *buf, size_t len)
{
    size_t n = 0;

    for (n = 0; n < TEE_NUM_PARAMS; n++) {
        uint32_t f = TEE_MEMORY_ACCESS_ANY_OWNER;

        switch (TEE_PARAM_TYPE_GET(ta_param_types, n)) {
        case TEE_PARAM_TYPE_MEMREF_OUTPUT:
        case TEE_PARAM_TYPE_MEMREF_INOUT:
            f |= TEE_MEMORY_ACCESS_WRITE;
            fallthrough;
        case TEE_PARAM_TYPE_MEMREF_INPUT:
            f |= TEE_MEMORY_ACCESS_READ;
            if (bufs_intersect(buf, len,
                              ta_params[n].memref.buffer,
                              ta_params[n].memref.size)) {
                /*
                 * GP标准：共享内存的预期行为
                 * 当缓冲区与TA参数重叠时，必须检查权限兼容性
                 * 如果未设置ANY_OWNER标志，则共享内存无法满足
                 * 读一致性、读写一致性、不可观察性要求
                 */
                if (!(flags & TEE_MEMORY_ACCESS_ANY_OWNER)) {
                    // 共享内存不满足一致性保证，必须拒绝
                    return TEE_ERROR_ACCESS_DENIED;
                }

                // 检查权限是否兼容
                if ((flags & f) != flags)
                    return TEE_ERROR_ACCESS_DENIED;
            }
            break;
        default:
            break;
        }
    }

    return TEE_SUCCESS;
}

/**
 * 检查缓冲区是否重叠
 */
static bool bufs_intersect(void *buf1, size_t sz1, void *buf2, size_t sz2)
{
    vaddr_t b1 = (vaddr_t)buf1;
    vaddr_t b2 = (vaddr_t)buf2;
    vaddr_t e1 = b1 + sz1 - 1;
    vaddr_t e2 = b2 + sz2 - 1;

    if (!sz1 || !sz2)
        return false;

    if (e1 < b2 || e2 < b1)
        return false;

    return true;
}
### 4.3 Trusty分配器感知的堆检查函数
```c
/**
 * 检查缓冲区是否与堆内存重叠 - Trusty分配器感知
 */
static bool trusty_buffer_overlaps_heap(void *buffer, size_t size)
{
    if (!buffer || !size)
        return false;

#ifdef RCTEE_TA_ALLOCATOR_SCUDO
    /* scudo版本 */
    void *heap_base = scudo_get_heap_base();
    size_t heap_size = scudo_get_heap_size();
#else
    /* dlmalloc版本 */
    void *heap_base = dlmalloc_get_heap_base();
    size_t heap_size = dlmalloc_footprint();
#endif

    if (!heap_base || heap_size == 0)
        return false;

    /* 计算重叠 */
    uintptr_t heap_start = (uintptr_t)heap_base;
    uintptr_t heap_end = heap_start + heap_size;
    uintptr_t buf_start = (uintptr_t)buffer;
    uintptr_t buf_end = buf_start + size;

    return (buf_start < heap_end && buf_end > heap_start);
}

/**
 * 检查缓冲区是否在已分配的堆块内 - Trusty分配器感知
 */
static bool trusty_buffer_is_within_allocated(void *buffer, size_t size)
{
    if (!buffer || !size)
        return false;

#ifdef RCTEE_TA_ALLOCATOR_SCUDO
    /* scudo版本 */
    size_t usable_size = SCUDO_PREFIX(malloc_usable_size)(buffer);
#else
    /* dlmalloc版本 */
    size_t usable_size = dlmalloc_usable_size(buffer);
#endif

    /* 如果usable_size为0，说明不是有效的已分配块 */
    if (usable_size == 0)
        return false;

    /* 检查整个缓冲区是否在已分配块内 */
    return (size <= usable_size);
}
```

## 5. 系统调用表更新

### 5.1 添加系统调用

```c
// kernel/rctee/lib/rctee/include/syscall_table.h
/* 内存权限检查系统调用 */
DEF_SYSCALL(0x42, _rctee_check_memory_access_rights, long, 3, void *buffer, uint32_t size, uint32_t access_flags)
```

## 6. 优势分析

### 6.1 与OP-TEE完全等价
- **预先权限验证**：通过系统调用在用户空间预先验证所有权限
- **完整的MMU查询**：内核可以完整查询页面的MMU权限标志
- **精确的错误检测**：能够精确识别权限不足的原因

### 6.2 严格符合GP标准
- **完整的安全保证**：读写成功、一致性、不可观察性
- **准确的内存类型识别**：堆、栈、数据段、代码段、共享内存
- **正确的参数冲突检测**：与当前TA调用参数的冲突检查

### 6.3 保持trusty架构一致性
- **系统调用机制**：使用trusty标准的系统调用接口
- **内核权限管理**：利用内核的完整MMU管理能力
- **用户空间简洁性**：用户空间逻辑清晰，复杂度在内核

## 7. 实现优先级

1. **第一优先级**：实现`_rctee_check_memory_access_rights`系统调用
2. **第二优先级**：实现Trusty分配器感知的堆检查函数
3. **第三优先级**：实现TA参数冲突检查和内存类型检测

## 8. 总结

### 8.1 方案优势
1. **完全等价于OP-TEE**：通过系统调用提供相同的权限查询能力
2. **严格符合GP标准**：满足所有安全保证和检查要求
3. **保持架构一致性**：使用trusty标准的系统调用机制
4. **渐进式实现**：可以分阶段实现，逐步完善功能

这个系统调用扩展方案是实现GP标准兼容的最佳选择，能够提供与OP-TEE完全等价的功能！
