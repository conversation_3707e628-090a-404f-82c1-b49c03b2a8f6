# TEE_CheckMemoryAccessRights 系统调用扩展方案

## 1. 方案概述

通过在trusty中增加新的系统调用，使用户空间能够查询内存权限，从而达到与OP-TEE完全一样的效果，严格符合GP标准要求。

## 2. 新增系统调用设计

### 2.1 内存权限查询系统调用

**系统调用定义**：
```c
// 系统调用号：0x42 (在现有系统调用表后添加)
DEF_SYSCALL(0x42, check_memory_access_rights, long, 3, void *buffer, uint32_t size, uint32_t access_flags)
```

**用户空间接口**：
```c
// user/base/lib/libc-rctee/include/rctee_syscalls.h
long _rctee_check_memory_access_rights(void *buffer, uint32_t size, uint32_t access_flags);

// user/base/lib/libc-rctee/memory_check.c
int check_memory_access_rights(void *buffer, size_t size, uint32_t access_flags) {
    if (size > UINT32_MAX) {
        return ERR_INVALID_ARGS;
    }
    return _rctee_check_memory_access_rights(buffer, (uint32_t)size, access_flags);
}
```

### 2.2 内存类型查询系统调用

**系统调用定义**：
```c
// 系统调用号：0x43
DEF_SYSCALL(0x43, get_memory_type, long, 3, void *buffer, uint32_t size, uint32_t *mem_type)
```

**用户空间接口**：
```c
typedef enum {
    MEMORY_TYPE_TA_HEAP = 1,
    MEMORY_TYPE_TA_STACK = 2,
    MEMORY_TYPE_TA_DATA = 3,
    MEMORY_TYPE_TA_CONST = 4,
    MEMORY_TYPE_TA_CODE = 5,
    MEMORY_TYPE_TEE_PARAM = 6,
    MEMORY_TYPE_SHARED_CA = 7,
    MEMORY_TYPE_UNKNOWN = 0
} memory_type_t;

int get_memory_type(void *buffer, size_t size, memory_type_t *mem_type);
```

### 2.3 TA参数信息查询系统调用

**系统调用定义**：
```c
// 系统调用号：0x44
DEF_SYSCALL(0x44, get_ta_param_info, long, 2, struct ta_param_info *param_info, uint32_t *param_count)
```

**用户空间接口**：
```c
struct ta_param_info {
    void *buffer;
    uint32_t size;
    uint32_t param_type;  // TEE_PARAM_TYPE_*
    uint32_t flags;       // 权限标志
};

int get_ta_param_info(struct ta_param_info *param_info, uint32_t *param_count);
```

## 3. 内核实现方案

### 3.1 内存权限查询实现

```c
// kernel/rctee/lib/rctee/rctee_core/syscall.c
long sys_check_memory_access_rights(user_addr_t buffer, uint32_t size, uint32_t access_flags) {
    struct rctee_app* rctee_app = current_rctee_app();
    vaddr_t vaddr = (vaddr_t)buffer;
    
    // 基本参数检查
    if (!size) return NO_ERROR;  // GP标准：零大小返回成功
    if (!buffer) return ERR_ACCESS_DENIED;  // NULL指针拒绝
    
    // 地址溢出检查
    if (vaddr + size < vaddr) return ERR_ACCESS_DENIED;
    
    // 检查地址是否在用户空间范围内
    if (!is_user_address(vaddr) || !is_user_address(vaddr + size - 1))
        return ERR_ACCESS_DENIED;
    
    // 按页面检查内存权限
    return check_memory_pages_access_rights(rctee_app->aspace, vaddr, size, access_flags);
}

// 按页面检查内存访问权限（类似OP-TEE的实现）
static long check_memory_pages_access_rights(vmm_aspace_t *aspace, vaddr_t vaddr, 
                                            uint32_t size, uint32_t access_flags) {
    size_t offset = vaddr & (PAGE_SIZE - 1);
    size_t aligned_size = round_up(size + offset, PAGE_SIZE);
    vaddr_t aligned_vaddr = round_down(vaddr, PAGE_SIZE);
    
    while (aligned_size > 0) {
        uint arch_mmu_flags;
        paddr_t paddr;
        
        // 查询页面的MMU权限
        status_t ret = arch_mmu_query(&aspace->arch_aspace, aligned_vaddr, &paddr, &arch_mmu_flags);
        if (ret != NO_ERROR) {
            return ERR_ACCESS_DENIED;  // 页面未映射
        }
        
        // 检查读权限
        if ((access_flags & TEE_MEMORY_ACCESS_READ) && 
            !(arch_mmu_flags & ARCH_MMU_FLAG_PERM_USER)) {
            return ERR_ACCESS_DENIED;
        }
        
        // 检查写权限
        if ((access_flags & TEE_MEMORY_ACCESS_WRITE) && 
            (arch_mmu_flags & ARCH_MMU_FLAG_PERM_RO)) {
            return ERR_ACCESS_DENIED;
        }
        
        aligned_vaddr += PAGE_SIZE;
        aligned_size -= PAGE_SIZE;
    }
    
    return NO_ERROR;
}
```

### 3.2 内存类型查询实现

```c
long sys_get_memory_type(user_addr_t buffer, uint32_t size, user_addr_t mem_type_ptr) {
    struct rctee_app* rctee_app = current_rctee_app();
    vaddr_t vaddr = (vaddr_t)buffer;
    uint32_t mem_type = MEMORY_TYPE_UNKNOWN;
    
    // 检查是否为堆内存
    if (is_heap_memory(rctee_app, vaddr, size)) {
        mem_type = MEMORY_TYPE_TA_HEAP;
    }
    // 检查是否为栈内存
    else if (is_stack_memory(rctee_app, vaddr, size)) {
        mem_type = MEMORY_TYPE_TA_STACK;
    }
    // 检查是否为数据段
    else if (is_data_segment(rctee_app, vaddr, size)) {
        mem_type = MEMORY_TYPE_TA_DATA;
    }
    // 检查是否为代码段
    else if (is_code_segment(rctee_app, vaddr, size)) {
        mem_type = MEMORY_TYPE_TA_CODE;
    }
    // 检查是否为共享内存
    else if (is_shared_memory(rctee_app, vaddr, size)) {
        mem_type = MEMORY_TYPE_SHARED_CA;
    }
    
    // 将结果复制到用户空间
    return copy_to_user(mem_type_ptr, &mem_type, sizeof(mem_type));
}
```

### 3.3 TA参数信息查询实现

```c
long sys_get_ta_param_info(user_addr_t param_info_ptr, user_addr_t param_count_ptr) {
    struct rctee_app* rctee_app = current_rctee_app();
    
    // 获取当前TA调用的参数信息
    // TODO: 需要在TA调用时保存参数信息到rctee_app结构中
    
    uint32_t count = rctee_app->current_param_count;
    struct ta_param_info *params = rctee_app->current_params;
    
    // 复制参数数量到用户空间
    status_t ret = copy_to_user(param_count_ptr, &count, sizeof(count));
    if (ret != NO_ERROR) return ret;
    
    // 复制参数信息到用户空间
    if (count > 0 && param_info_ptr) {
        ret = copy_to_user(param_info_ptr, params, count * sizeof(struct ta_param_info));
        if (ret != NO_ERROR) return ret;
    }
    
    return NO_ERROR;
}
```

## 4. 用户空间完整实现

### 4.1 TEE_CheckMemoryAccessRights完整实现

```c
// user/base/lib/libutee/tee_memory_check.c
# TEE_CheckMemoryAccessRights系统调用扩展方案

## 1. 用户空间实现 (lib/libutee/tee_api.c)

```c
/**
 * 检查内存访问权限的辅助函数 - 检查与TA参数的重叠
 */
static TEE_Result check_mem_access_rights_params(uint32_t flags, void *buf, size_t len)
{
    size_t n = 0;

    for (n = 0; n < TEE_NUM_PARAMS; n++) {
        uint32_t f = TEE_MEMORY_ACCESS_ANY_OWNER;

        switch (TEE_PARAM_TYPE_GET(ta_param_types, n)) {
        case TEE_PARAM_TYPE_MEMREF_OUTPUT:
        case TEE_PARAM_TYPE_MEMREF_INOUT:
            f |= TEE_MEMORY_ACCESS_WRITE;
            fallthrough;
        case TEE_PARAM_TYPE_MEMREF_INPUT:
            f |= TEE_MEMORY_ACCESS_READ;
            if (bufs_intersect(buf, len,
                              ta_params[n].memref.buffer,
                              ta_params[n].memref.size)) {
                if ((flags & f) != flags)
                    return TEE_ERROR_ACCESS_DENIED;
            }
            break;
        default:
            break;
        }
    }

    return TEE_SUCCESS;
}

/**
 * 检查缓冲区是否重叠
 */
static bool bufs_intersect(void *buf1, size_t sz1, void *buf2, size_t sz2)
{
    vaddr_t b1 = (vaddr_t)buf1;
    vaddr_t b2 = (vaddr_t)buf2;
    vaddr_t e1 = b1 + sz1 - 1;
    vaddr_t e2 = b2 + sz2 - 1;

    if (!sz1 || !sz2)
        return false;

    if (e1 < b2 || e2 < b1)
        return false;

    return true;
}

/**
 * GP标准TEE_CheckMemoryAccessRights实现
 */
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size)
{
    uint32_t flags = accessFlags;

    /* GP标准：size为0时直接返回成功 */
    if (!size)
        return TEE_SUCCESS;

    /*
     * 第一步：通过系统调用检查内存映射权限
     * 这是核心的MMU权限检查，由内核完成
     */
    if (_utee_check_memory_access_rights(accessFlags, buffer, size))
        return TEE_ERROR_ACCESS_DENIED;

    /*
     * 第二步：检查与TA参数的权限兼容性
     * 清除扩展标志，只保留基本访问标志
     */
    flags &= TEE_MEMORY_ACCESS_READ | TEE_MEMORY_ACCESS_WRITE |
             TEE_MEMORY_ACCESS_ANY_OWNER;
    if (check_mem_access_rights_params(flags, buffer, size))
        return TEE_ERROR_ACCESS_DENIED;

    /*
     * 第三步：检查堆内存重叠（如果适用）
     * 防止TA暴露私有堆内存
     */
    if (malloc_buffer_overlaps_heap(buffer, size) &&
        !malloc_buffer_is_within_allocated(buffer, size))
        return TEE_ERROR_ACCESS_DENIED;

    return TEE_SUCCESS;
}
    // 第一步：基本参数检查
    if (!size) return TEE_SUCCESS;
    if (!buffer) return TEE_ERROR_ACCESS_DENIED;
    if (!is_valid_access_flags(accessFlags)) return TEE_ERROR_BAD_PARAMETERS;
    
    // 第二步：调用系统调用检查内存权限（与OP-TEE等价）
    int ret = check_memory_access_rights(buffer, size, accessFlags);
    if (ret != 0) return TEE_ERROR_ACCESS_DENIED;
    
    // 第三步：检查内存类型和最小权限要求
    memory_type_t mem_type;
    ret = get_memory_type(buffer, size, &mem_type);
    if (ret != 0) return TEE_ERROR_ACCESS_DENIED;
    
    if (!check_minimum_permissions(mem_type, accessFlags))
        return TEE_ERROR_ACCESS_DENIED;
    
    // 第四步：检查TA参数冲突（如果未设置ANY_OWNER）
    if (!(accessFlags & TEE_MEMORY_ACCESS_ANY_OWNER)) {
        ret = check_ta_param_conflicts(accessFlags, buffer, size);
        if (ret != TEE_SUCCESS) return ret;
        
        // 检查共享内存一致性保证
        if (mem_type == MEMORY_TYPE_SHARED_CA)
            return TEE_ERROR_ACCESS_DENIED;  // 共享内存无法保证一致性
    }
    
    return TEE_SUCCESS;
}
```

## 5. 系统调用表更新

### 5.1 添加新系统调用

```c
// kernel/rctee/lib/rctee/include/syscall_table.h
/* 内存权限检查相关系统调用 */
DEF_SYSCALL(0x42, check_memory_access_rights, long, 3, void *buffer, uint32_t size, uint32_t access_flags)
DEF_SYSCALL(0x43, get_memory_type, long, 3, void *buffer, uint32_t size, uint32_t *mem_type)
DEF_SYSCALL(0x44, get_ta_param_info, long, 2, struct ta_param_info *param_info, uint32_t *param_count)
```

## 6. 优势分析

### 6.1 与OP-TEE完全等价
- **预先权限验证**：通过系统调用在用户空间预先验证所有权限
- **完整的MMU查询**：内核可以完整查询页面的MMU权限标志
- **精确的错误检测**：能够精确识别权限不足的原因

### 6.2 严格符合GP标准
- **完整的安全保证**：读写成功、一致性、不可观察性
- **准确的内存类型识别**：堆、栈、数据段、代码段、共享内存
- **正确的参数冲突检测**：与当前TA调用参数的冲突检查

### 6.3 保持trusty架构一致性
- **系统调用机制**：使用trusty标准的系统调用接口
- **内核权限管理**：利用内核的完整MMU管理能力
- **用户空间简洁性**：用户空间逻辑清晰，复杂度在内核

## 7. 实现优先级

1. **第一优先级**：实现`check_memory_access_rights`系统调用
2. **第二优先级**：实现`get_memory_type`系统调用  
3. **第三优先级**：实现`get_ta_param_info`系统调用和参数管理

## 8. 内核辅助函数实现

### 8.1 内存区域类型检测

```c
// 检查是否为堆内存
static bool is_heap_memory(struct rctee_app* app, vaddr_t vaddr, uint32_t size) {
    // 检查地址是否在堆区域范围内
    return (vaddr >= app->start_brk && vaddr + size <= app->cur_brk);
}

// 检查是否为栈内存
static bool is_stack_memory(struct rctee_app* app, vaddr_t vaddr, uint32_t size) {
    // 获取当前线程的栈信息
    thread_t *current = get_current_thread();
    vaddr_t stack_base = (vaddr_t)current->stack;
    vaddr_t stack_top = stack_base + current->stack_size;

    return (vaddr >= stack_base && vaddr + size <= stack_top);
}

// 检查是否为数据段
static bool is_data_segment(struct rctee_app* app, vaddr_t vaddr, uint32_t size) {
    // 检查是否在TA的数据段范围内
    // TODO: 需要从ELF加载信息中获取数据段范围
    return false;  // 暂时实现
}

// 检查是否为代码段
static bool is_code_segment(struct rctee_app* app, vaddr_t vaddr, uint32_t size) {
    // 检查是否在TA的代码段范围内
    // TODO: 需要从ELF加载信息中获取代码段范围
    return false;  // 暂时实现
}

// 检查是否为共享内存
static bool is_shared_memory(struct rctee_app* app, vaddr_t vaddr, uint32_t size) {
    // 检查是否为通过memref映射的共享内存
    // TODO: 遍历app的内存映射列表，查找共享内存区域
    return false;  // 暂时实现
}
```

### 8.2 GP标志与trusty MMU标志转换

```c
// GP TEE访问标志转换为trusty MMU标志
static uint32_t gp_flags_to_mmu_flags(uint32_t gp_flags) {
    uint32_t mmu_flags = 0;

    if (gp_flags & TEE_MEMORY_ACCESS_READ) {
        mmu_flags |= ARCH_MMU_FLAG_PERM_USER;
    }

    if (gp_flags & TEE_MEMORY_ACCESS_WRITE) {
        // 写权限要求非只读
        // 注意：ARCH_MMU_FLAG_PERM_RO表示只读，所以写权限是其反面
    }

    return mmu_flags;
}

// 检查MMU标志是否满足GP访问要求
static bool mmu_flags_satisfy_gp_access(uint32_t arch_mmu_flags, uint32_t gp_flags) {
    // 检查读权限
    if ((gp_flags & TEE_MEMORY_ACCESS_READ) &&
        !(arch_mmu_flags & ARCH_MMU_FLAG_PERM_USER)) {
        return false;
    }

    // 检查写权限
    if ((gp_flags & TEE_MEMORY_ACCESS_WRITE) &&
        (arch_mmu_flags & ARCH_MMU_FLAG_PERM_RO)) {
        return false;
    }

    return true;
}
```

## 9. TA参数管理扩展

### 9.1 rctee_app结构扩展

```c
// kernel/rctee/lib/rctee/include/rctee_app.h
struct rctee_app {
    // ... 现有字段 ...

    // 新增：当前调用的参数信息
    struct ta_param_info current_params[4];  // 最多4个参数
    uint32_t current_param_count;
    bool params_valid;  // 参数信息是否有效
};
```

### 9.2 TA调用时参数信息保存

```c
// 在TA入口点调用时保存参数信息
void save_ta_call_params(struct rctee_app* app, uint32_t param_types, TEE_Param params[4]) {
    app->current_param_count = 0;
    app->params_valid = true;

    for (int i = 0; i < 4; i++) {
        uint32_t param_type = TEE_PARAM_TYPE_GET(param_types, i);

        if (param_type == TEE_PARAM_TYPE_NONE) {
            continue;
        }

        struct ta_param_info* param_info = &app->current_params[app->current_param_count];
        param_info->param_type = param_type;

        switch (param_type) {
            case TEE_PARAM_TYPE_MEMREF_INPUT:
                param_info->buffer = params[i].memref.buffer;
                param_info->size = params[i].memref.size;
                param_info->flags = TEE_MEMORY_ACCESS_READ;
                break;

            case TEE_PARAM_TYPE_MEMREF_OUTPUT:
                param_info->buffer = params[i].memref.buffer;
                param_info->size = params[i].memref.size;
                param_info->flags = TEE_MEMORY_ACCESS_WRITE;
                break;

            case TEE_PARAM_TYPE_MEMREF_INOUT:
                param_info->buffer = params[i].memref.buffer;
                param_info->size = params[i].memref.size;
                param_info->flags = TEE_MEMORY_ACCESS_READ | TEE_MEMORY_ACCESS_WRITE;
                break;

            default:
                // 非内存引用参数
                param_info->buffer = NULL;
                param_info->size = 0;
                param_info->flags = 0;
                break;
        }

        app->current_param_count++;
    }
}

// 在TA调用结束时清理参数信息
void clear_ta_call_params(struct rctee_app* app) {
    app->params_valid = false;
    app->current_param_count = 0;
    memset(app->current_params, 0, sizeof(app->current_params));
}
```

## 10. 用户空间辅助函数

### 10.1 TA参数冲突检查

```c
// user/base/lib/libutee/tee_memory_check.c
static TEE_Result check_ta_param_conflicts(uint32_t access_flags, void *buffer, size_t size) {
    struct ta_param_info params[4];
    uint32_t param_count = 0;

    // 获取当前TA调用的参数信息
    int ret = get_ta_param_info(params, &param_count);
    if (ret != 0) {
        // 无法获取参数信息，保守处理
        return TEE_SUCCESS;
    }

    uintptr_t check_start = (uintptr_t)buffer;
    uintptr_t check_end = check_start + size;

    // 检查与每个参数的重叠和权限冲突
    for (uint32_t i = 0; i < param_count; i++) {
        if (!params[i].buffer || !params[i].size) {
            continue;  // 跳过非内存引用参数
        }

        uintptr_t param_start = (uintptr_t)params[i].buffer;
        uintptr_t param_end = param_start + params[i].size;

        // 检查内存区域是否重叠
        if (!(check_end <= param_start || check_start >= param_end)) {
            // 有重叠，检查权限是否冲突
            if ((access_flags & TEE_MEMORY_ACCESS_WRITE) &&
                !(params[i].flags & TEE_MEMORY_ACCESS_WRITE)) {
                // 请求写权限但参数只有读权限
                return TEE_ERROR_ACCESS_DENIED;
            }
        }
    }

    return TEE_SUCCESS;
}
```

### 10.2 最小权限检查

```c
static bool check_minimum_permissions(memory_type_t mem_type, uint32_t access_flags) {
    switch (mem_type) {
        case MEMORY_TYPE_TA_HEAP:
        case MEMORY_TYPE_TA_STACK:
        case MEMORY_TYPE_TA_DATA:
        case MEMORY_TYPE_TEE_PARAM:
            // GP标准：这些内存必须有读写权限
            return true;  // 允许任何访问

        case MEMORY_TYPE_TA_CONST:
            // GP标准：const变量只能读，不能写
            return !(access_flags & TEE_MEMORY_ACCESS_WRITE);

        case MEMORY_TYPE_TA_CODE:
            // GP标准：代码段可能有读权限，必须有执行权限
            return true;  // 允许读访问，执行权限由MMU控制

        case MEMORY_TYPE_SHARED_CA:
            // 共享内存权限需要与TEE_PARAM_TYPES对应
            return true;  // 由参数冲突检查处理

        default:
            return false;  // 未知类型拒绝访问
    }
}
```

## 11. 完整的流程图

这个扩展方案提供了与OP-TEE完全等价的实现能力，通过系统调用使用户空间能够：

1. **完整的MMU权限查询**：通过内核查询每个页面的实际权限
2. **精确的内存类型识别**：区分堆、栈、数据段、代码段、共享内存
3. **准确的参数冲突检测**：检查与当前TA调用参数的权限冲突
4. **严格的GP标准遵循**：满足所有安全保证和一致性要求

## 12. 实现计划和文件修改清单

### 12.1 第一阶段：核心系统调用实现

**内核侧修改**：
```
kernel/rctee/lib/rctee/include/syscall_table.h
├── 添加 DEF_SYSCALL(0x42, check_memory_access_rights, ...)
├── 添加 DEF_SYSCALL(0x43, get_memory_type, ...)
└── 添加 DEF_SYSCALL(0x44, get_ta_param_info, ...)

kernel/rctee/lib/rctee/rctee_core/syscall.c
├── 实现 sys_check_memory_access_rights()
├── 实现 sys_get_memory_type()
├── 实现 sys_get_ta_param_info()
└── 添加内存类型检测辅助函数

kernel/rctee/lib/rctee/include/rctee_app.h
└── 扩展 struct rctee_app 添加参数信息字段
```

**用户空间修改**：
```
user/base/lib/libc-rctee/include/rctee_syscalls.h
├── 声明 _rctee_check_memory_access_rights()
├── 声明 _rctee_get_memory_type()
└── 声明 _rctee_get_ta_param_info()

user/base/lib/libc-rctee/memory_check.c (新建)
├── 实现 check_memory_access_rights()
├── 实现 get_memory_type()
└── 实现 get_ta_param_info()

user/base/lib/libutee/include/tee_api_defines.h
└── 添加内存访问权限标志定义

user/base/lib/libutee/include/tee_internal_api.h
└── 声明 TEE_CheckMemoryAccessRights()

user/base/lib/libutee/tee_memory_check.c (新建)
└── 实现完整的 TEE_CheckMemoryAccessRights()
```

### 12.2 第二阶段：内存布局信息获取

**需要实现的功能**：
1. **ELF段信息保存**：在TA加载时保存代码段、数据段地址范围
2. **堆管理器集成**：与dlmalloc集成，提供堆内存检测接口
3. **栈信息获取**：获取当前线程栈的地址范围
4. **共享内存跟踪**：跟踪通过memref创建的共享内存区域

### 12.3 第三阶段：TA参数管理

**需要实现的功能**：
1. **参数信息保存**：在TA入口点调用时保存参数信息
2. **参数生命周期管理**：在TA调用结束时清理参数信息
3. **参数权限映射**：将TEE_PARAM_TYPE转换为访问权限标志

### 12.4 测试验证计划

**单元测试**：
```c
// 测试基本功能
test_zero_size_returns_success();
test_null_pointer_returns_denied();
test_invalid_flags_returns_bad_parameters();

// 测试内存类型检测
test_heap_memory_detection();
test_stack_memory_detection();
test_shared_memory_detection();

// 测试权限检查
test_read_only_memory_write_denied();
test_read_write_memory_access_allowed();
test_const_memory_write_denied();

// 测试参数冲突
test_overlapping_param_conflict();
test_non_overlapping_param_allowed();
```

**集成测试**：
```c
// 测试与OP-TEE的兼容性
test_optee_compatibility();
test_gp_standard_compliance();
test_shared_memory_consistency();
```

## 13. 性能影响分析

### 13.1 系统调用开销
- **check_memory_access_rights**：需要遍历页面查询MMU，开销较大
- **get_memory_type**：基于地址范围判断，开销较小
- **get_ta_param_info**：简单的内存复制，开销很小

### 13.2 优化策略
1. **页面权限缓存**：缓存最近查询的页面权限信息
2. **批量检查**：对连续页面进行批量权限查询
3. **快速路径**：对常见内存类型提供快速检测路径

## 14. 总结

### 14.1 方案优势
1. **完全等价于OP-TEE**：通过系统调用提供相同的权限查询能力
2. **严格符合GP标准**：满足所有安全保证和检查要求
3. **保持架构一致性**：使用trusty标准的系统调用机制
4. **渐进式实现**：可以分阶段实现，逐步完善功能

### 14.2 实现复杂度
- **内核实现**：中等复杂度，主要是MMU权限查询和内存类型检测
- **用户空间实现**：低复杂度，主要是系统调用封装和逻辑组合
- **测试验证**：高复杂度，需要全面的兼容性和正确性测试

### 14.3 推荐实施
这个系统调用扩展方案是实现GP标准兼容的最佳选择，能够提供与OP-TEE完全等价的功能，建议优先实施！
