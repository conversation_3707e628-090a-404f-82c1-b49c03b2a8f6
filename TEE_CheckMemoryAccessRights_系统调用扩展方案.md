# TEE_CheckMemoryAccessRights 系统调用扩展方案

## 1. 方案概述

通过在trusty中增加新的系统调用，使用户空间能够查询内存权限，从而达到与OP-TEE完全一样的效果，严格符合GP标准要求。

## 2. 新增系统调用设计

### 2.1 内存权限查询系统调用

**系统调用定义**：
```c
// 系统调用号：0x42 (在现有系统调用表后添加)
DEF_SYSCALL(0x42, check_memory_access_rights, long, 3, void *buffer, uint32_t size, uint32_t access_flags)
```

**用户空间接口**：
```c
// user/base/lib/libc-rctee/include/rctee_syscalls.h
long _rctee_check_memory_access_rights(void *buffer, uint32_t size, uint32_t access_flags);

// user/base/lib/libc-rctee/memory_check.c
int check_memory_access_rights(void *buffer, size_t size, uint32_t access_flags) {
    if (size > UINT32_MAX) {
        return ERR_INVALID_ARGS;
    }
    return _rctee_check_memory_access_rights(buffer, (uint32_t)size, access_flags);
}
```

### 2.2 内存类型查询系统调用

**系统调用定义**：
```c
// 系统调用号：0x43
DEF_SYSCALL(0x43, get_memory_type, long, 3, void *buffer, uint32_t size, uint32_t *mem_type)
```

**用户空间接口**：
```c
typedef enum {
    MEMORY_TYPE_TA_HEAP = 1,
    MEMORY_TYPE_TA_STACK = 2,
    MEMORY_TYPE_TA_DATA = 3,
    MEMORY_TYPE_TA_CONST = 4,
    MEMORY_TYPE_TA_CODE = 5,
    MEMORY_TYPE_TEE_PARAM = 6,
    MEMORY_TYPE_SHARED_CA = 7,
    MEMORY_TYPE_UNKNOWN = 0
} memory_type_t;

int get_memory_type(void *buffer, size_t size, memory_type_t *mem_type);
```

### 2.3 TA参数信息查询系统调用

**系统调用定义**：
```c
// 系统调用号：0x44
DEF_SYSCALL(0x44, get_ta_param_info, long, 2, struct ta_param_info *param_info, uint32_t *param_count)
```

**用户空间接口**：
```c
struct ta_param_info {
    void *buffer;
    uint32_t size;
    uint32_t param_type;  // TEE_PARAM_TYPE_*
    uint32_t flags;       // 权限标志
};

int get_ta_param_info(struct ta_param_info *param_info, uint32_t *param_count);
```

## 3. 内核实现方案

### 3.1 内存权限查询实现

```c
// kernel/rctee/lib/rctee/rctee_core/syscall.c
long sys_check_memory_access_rights(user_addr_t buffer, uint32_t size, uint32_t access_flags) {
    struct rctee_app* rctee_app = current_rctee_app();
    vaddr_t vaddr = (vaddr_t)buffer;
    
    // 基本参数检查
    if (!size) return NO_ERROR;  // GP标准：零大小返回成功
    if (!buffer) return ERR_ACCESS_DENIED;  // NULL指针拒绝
    
    // 地址溢出检查
    if (vaddr + size < vaddr) return ERR_ACCESS_DENIED;
    
    // 检查地址是否在用户空间范围内
    if (!is_user_address(vaddr) || !is_user_address(vaddr + size - 1))
        return ERR_ACCESS_DENIED;
    
    // 按页面检查内存权限
    return check_memory_pages_access_rights(rctee_app->aspace, vaddr, size, access_flags);
}

// 按页面检查内存访问权限（类似OP-TEE的实现）
static long check_memory_pages_access_rights(vmm_aspace_t *aspace, vaddr_t vaddr, 
                                            uint32_t size, uint32_t access_flags) {
    size_t offset = vaddr & (PAGE_SIZE - 1);
    size_t aligned_size = round_up(size + offset, PAGE_SIZE);
    vaddr_t aligned_vaddr = round_down(vaddr, PAGE_SIZE);
    
    while (aligned_size > 0) {
        uint arch_mmu_flags;
        paddr_t paddr;
        
        // 查询页面的MMU权限
        status_t ret = arch_mmu_query(&aspace->arch_aspace, aligned_vaddr, &paddr, &arch_mmu_flags);
        if (ret != NO_ERROR) {
            return ERR_ACCESS_DENIED;  // 页面未映射
        }
        
        // 检查读权限
        if ((access_flags & TEE_MEMORY_ACCESS_READ) && 
            !(arch_mmu_flags & ARCH_MMU_FLAG_PERM_USER)) {
            return ERR_ACCESS_DENIED;
        }
        
        // 检查写权限
        if ((access_flags & TEE_MEMORY_ACCESS_WRITE) && 
            (arch_mmu_flags & ARCH_MMU_FLAG_PERM_RO)) {
            return ERR_ACCESS_DENIED;
        }
        
        aligned_vaddr += PAGE_SIZE;
        aligned_size -= PAGE_SIZE;
    }
    
    return NO_ERROR;
}
```

### 3.2 内存类型查询实现

```c
long sys_get_memory_type(user_addr_t buffer, uint32_t size, user_addr_t mem_type_ptr) {
    struct rctee_app* rctee_app = current_rctee_app();
    vaddr_t vaddr = (vaddr_t)buffer;
    uint32_t mem_type = MEMORY_TYPE_UNKNOWN;
    
    // 检查是否为堆内存
    if (is_heap_memory(rctee_app, vaddr, size)) {
        mem_type = MEMORY_TYPE_TA_HEAP;
    }
    // 检查是否为栈内存
    else if (is_stack_memory(rctee_app, vaddr, size)) {
        mem_type = MEMORY_TYPE_TA_STACK;
    }
    // 检查是否为数据段
    else if (is_data_segment(rctee_app, vaddr, size)) {
        mem_type = MEMORY_TYPE_TA_DATA;
    }
    // 检查是否为代码段
    else if (is_code_segment(rctee_app, vaddr, size)) {
        mem_type = MEMORY_TYPE_TA_CODE;
    }
    // 检查是否为共享内存
    else if (is_shared_memory(rctee_app, vaddr, size)) {
        mem_type = MEMORY_TYPE_SHARED_CA;
    }
    
    // 将结果复制到用户空间
    return copy_to_user(mem_type_ptr, &mem_type, sizeof(mem_type));
}
```

### 3.3 TA参数信息查询实现

```c
long sys_get_ta_param_info(user_addr_t param_info_ptr, user_addr_t param_count_ptr) {
    struct rctee_app* rctee_app = current_rctee_app();
    
    // 获取当前TA调用的参数信息
    // TODO: 需要在TA调用时保存参数信息到rctee_app结构中
    
    uint32_t count = rctee_app->current_param_count;
    struct ta_param_info *params = rctee_app->current_params;
    
    // 复制参数数量到用户空间
    status_t ret = copy_to_user(param_count_ptr, &count, sizeof(count));
    if (ret != NO_ERROR) return ret;
    
    // 复制参数信息到用户空间
    if (count > 0 && param_info_ptr) {
        ret = copy_to_user(param_info_ptr, params, count * sizeof(struct ta_param_info));
        if (ret != NO_ERROR) return ret;
    }
    
    return NO_ERROR;
}
```

## 4. 用户空间完整实现

### 4.1 TEE_CheckMemoryAccessRights完整实现

## 4. 用户空间实现

### 4.1 TEE_CheckMemoryAccessRights主函数
```c
// user/base/lib/libutee/tee_api.c

/**
 * GP标准TEE_CheckMemoryAccessRights实现
 */
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size)
{
    uint32_t flags = accessFlags;

    /* GP标准：size为0时直接返回成功 */
    if (!size)
        return TEE_SUCCESS;

    /*
     * 第一步：通过系统调用检查内存映射权限
     * 这是核心的MMU权限检查，由内核完成
     */
    if (_rctee_check_memory_access_rights(buffer, size, accessFlags))
        return TEE_ERROR_ACCESS_DENIED;

    /*
     * 第二步：检查与TA参数的权限兼容性
     * 清除扩展标志，只保留基本访问标志
     */
    flags &= TEE_MEMORY_ACCESS_READ | TEE_MEMORY_ACCESS_WRITE |
             TEE_MEMORY_ACCESS_ANY_OWNER;
    if (check_mem_access_rights_params(flags, buffer, size))
        return TEE_ERROR_ACCESS_DENIED;

    /*
     * 第三步：检查堆内存重叠（如果适用）
     * 防止TA暴露私有堆内存 - 支持Trusty分配器
     */
    if (trusty_buffer_overlaps_heap(buffer, size) &&
        !trusty_buffer_is_within_allocated(buffer, size))
        return TEE_ERROR_ACCESS_DENIED;

    return TEE_SUCCESS;
}
```

### 4.2 辅助函数实现
```c
/**
 * 检查内存访问权限的辅助函数 - 检查与TA参数的重叠
 */
static TEE_Result check_mem_access_rights_params(uint32_t flags, void *buf, size_t len)
{
    size_t n = 0;

    for (n = 0; n < TEE_NUM_PARAMS; n++) {
        uint32_t f = TEE_MEMORY_ACCESS_ANY_OWNER;

        switch (TEE_PARAM_TYPE_GET(ta_param_types, n)) {
        case TEE_PARAM_TYPE_MEMREF_OUTPUT:
        case TEE_PARAM_TYPE_MEMREF_INOUT:
            f |= TEE_MEMORY_ACCESS_WRITE;
            fallthrough;
        case TEE_PARAM_TYPE_MEMREF_INPUT:
            f |= TEE_MEMORY_ACCESS_READ;
            if (bufs_intersect(buf, len,
                              ta_params[n].memref.buffer,
                              ta_params[n].memref.size)) {
                if ((flags & f) != flags)
                    return TEE_ERROR_ACCESS_DENIED;
            }
            break;
        default:
            break;
        }
    }

    return TEE_SUCCESS;
}

/**
 * 检查缓冲区是否重叠
 */
static bool bufs_intersect(void *buf1, size_t sz1, void *buf2, size_t sz2)
{
    vaddr_t b1 = (vaddr_t)buf1;
    vaddr_t b2 = (vaddr_t)buf2;
    vaddr_t e1 = b1 + sz1 - 1;
    vaddr_t e2 = b2 + sz2 - 1;

    if (!sz1 || !sz2)
        return false;

    if (e1 < b2 || e2 < b1)
        return false;

    return true;
}
### 4.3 Trusty分配器感知的堆检查函数
```c
/**
 * 检查缓冲区是否与堆内存重叠 - Trusty分配器感知
 */
static bool trusty_buffer_overlaps_heap(void *buffer, size_t size)
{
    if (!buffer || !size)
        return false;

#ifdef RCTEE_TA_ALLOCATOR_SCUDO
    /* scudo版本 */
    void *heap_base = scudo_get_heap_base();
    size_t heap_size = scudo_get_heap_size();
#else
    /* dlmalloc版本 */
    void *heap_base = dlmalloc_get_heap_base();
    size_t heap_size = dlmalloc_footprint();
#endif

    if (!heap_base || heap_size == 0)
        return false;

    /* 计算重叠 */
    uintptr_t heap_start = (uintptr_t)heap_base;
    uintptr_t heap_end = heap_start + heap_size;
    uintptr_t buf_start = (uintptr_t)buffer;
    uintptr_t buf_end = buf_start + size;

    return (buf_start < heap_end && buf_end > heap_start);
}

/**
 * 检查缓冲区是否在已分配的堆块内 - Trusty分配器感知
 */
static bool trusty_buffer_is_within_allocated(void *buffer, size_t size)
{
    if (!buffer || !size)
        return false;

#ifdef RCTEE_TA_ALLOCATOR_SCUDO
    /* scudo版本 */
    size_t usable_size = SCUDO_PREFIX(malloc_usable_size)(buffer);
#else
    /* dlmalloc版本 */
    size_t usable_size = dlmalloc_usable_size(buffer);
#endif

    /* 如果usable_size为0，说明不是有效的已分配块 */
    if (usable_size == 0)
        return false;

    /* 检查整个缓冲区是否在已分配块内 */
    return (size <= usable_size);
}
```

## 5. 系统调用表更新

### 5.1 添加新系统调用

```c
// kernel/rctee/lib/rctee/include/syscall_table.h
/* 内存权限检查相关系统调用 */
DEF_SYSCALL(0x42, check_memory_access_rights, long, 3, void *buffer, uint32_t size, uint32_t access_flags)
DEF_SYSCALL(0x43, get_memory_type, long, 3, void *buffer, uint32_t size, uint32_t *mem_type)
DEF_SYSCALL(0x44, get_ta_param_info, long, 2, struct ta_param_info *param_info, uint32_t *param_count)
```

## 6. 优势分析

### 6.1 与OP-TEE完全等价
- **预先权限验证**：通过系统调用在用户空间预先验证所有权限
- **完整的MMU查询**：内核可以完整查询页面的MMU权限标志
- **精确的错误检测**：能够精确识别权限不足的原因

### 6.2 严格符合GP标准
- **完整的安全保证**：读写成功、一致性、不可观察性
- **准确的内存类型识别**：堆、栈、数据段、代码段、共享内存
- **正确的参数冲突检测**：与当前TA调用参数的冲突检查

### 6.3 保持trusty架构一致性
- **系统调用机制**：使用trusty标准的系统调用接口
- **内核权限管理**：利用内核的完整MMU管理能力
- **用户空间简洁性**：用户空间逻辑清晰，复杂度在内核

## 7. 实现优先级

1. **第一优先级**：实现`check_memory_access_rights`系统调用
2. **第二优先级**：实现`get_memory_type`系统调用  
3. **第三优先级**：实现`get_ta_param_info`系统调用和参数管理



## 9. TA参数管理扩展

### 9.1 rctee_app结构扩展

```c
// kernel/rctee/lib/rctee/include/rctee_app.h
struct rctee_app {
    // ... 现有字段 ...

    // 新增：当前调用的参数信息
    struct ta_param_info current_params[4];  // 最多4个参数
    uint32_t current_param_count;
    bool params_valid;  // 参数信息是否有效
};
```

### 9.2 TA调用时参数信息保存

```c
// 在TA入口点调用时保存参数信息
void save_ta_call_params(struct rctee_app* app, uint32_t param_types, TEE_Param params[4]) {
    app->current_param_count = 0;
    app->params_valid = true;

    for (int i = 0; i < 4; i++) {
        uint32_t param_type = TEE_PARAM_TYPE_GET(param_types, i);

        if (param_type == TEE_PARAM_TYPE_NONE) {
            continue;
        }

        struct ta_param_info* param_info = &app->current_params[app->current_param_count];
        param_info->param_type = param_type;

        switch (param_type) {
            case TEE_PARAM_TYPE_MEMREF_INPUT:
                param_info->buffer = params[i].memref.buffer;
                param_info->size = params[i].memref.size;
                param_info->flags = TEE_MEMORY_ACCESS_READ;
                break;

            case TEE_PARAM_TYPE_MEMREF_OUTPUT:
                param_info->buffer = params[i].memref.buffer;
                param_info->size = params[i].memref.size;
                param_info->flags = TEE_MEMORY_ACCESS_WRITE;
                break;

            case TEE_PARAM_TYPE_MEMREF_INOUT:
                param_info->buffer = params[i].memref.buffer;
                param_info->size = params[i].memref.size;
                param_info->flags = TEE_MEMORY_ACCESS_READ | TEE_MEMORY_ACCESS_WRITE;
                break;

            default:
                // 非内存引用参数
                param_info->buffer = NULL;
                param_info->size = 0;
                param_info->flags = 0;
                break;
        }

        app->current_param_count++;
    }
}

// 在TA调用结束时清理参数信息
void clear_ta_call_params(struct rctee_app* app) {
    app->params_valid = false;
    app->current_param_count = 0;
    memset(app->current_params, 0, sizeof(app->current_params));
}
```

## 10. 用户空间辅助函数

### 10.1 TA参数冲突检查

```c
// user/base/lib/libutee/tee_memory_check.c
static TEE_Result check_ta_param_conflicts(uint32_t access_flags, void *buffer, size_t size) {
    struct ta_param_info params[4];
    uint32_t param_count = 0;

    // 获取当前TA调用的参数信息
    int ret = get_ta_param_info(params, &param_count);
    if (ret != 0) {
        // 无法获取参数信息，保守处理
        return TEE_SUCCESS;
    }

    uintptr_t check_start = (uintptr_t)buffer;
    uintptr_t check_end = check_start + size;

    // 检查与每个参数的重叠和权限冲突
    for (uint32_t i = 0; i < param_count; i++) {
        if (!params[i].buffer || !params[i].size) {
            continue;  // 跳过非内存引用参数
        }

        uintptr_t param_start = (uintptr_t)params[i].buffer;
        uintptr_t param_end = param_start + params[i].size;

        // 检查内存区域是否重叠
        if (!(check_end <= param_start || check_start >= param_end)) {
            // 有重叠，检查权限是否冲突
            if ((access_flags & TEE_MEMORY_ACCESS_WRITE) &&
                !(params[i].flags & TEE_MEMORY_ACCESS_WRITE)) {
                // 请求写权限但参数只有读权限
                return TEE_ERROR_ACCESS_DENIED;
            }
        }
    }

    return TEE_SUCCESS;
}
```

### 10.2 最小权限检查

```c
static bool check_minimum_permissions(memory_type_t mem_type, uint32_t access_flags) {
    switch (mem_type) {
        case MEMORY_TYPE_TA_HEAP:
        case MEMORY_TYPE_TA_STACK:
        case MEMORY_TYPE_TA_DATA:
        case MEMORY_TYPE_TEE_PARAM:
            // GP标准：这些内存必须有读写权限
            return true;  // 允许任何访问

        case MEMORY_TYPE_TA_CONST:
            // GP标准：const变量只能读，不能写
            return !(access_flags & TEE_MEMORY_ACCESS_WRITE);

        case MEMORY_TYPE_TA_CODE:
            // GP标准：代码段可能有读权限，必须有执行权限
            return true;  // 允许读访问，执行权限由MMU控制

        case MEMORY_TYPE_SHARED_CA:
            // 共享内存权限需要与TEE_PARAM_TYPES对应
            return true;  // 由参数冲突检查处理

        default:
            return false;  // 未知类型拒绝访问
    }
}
```

## 7. 实现优先级

1. **第一优先级**：实现`_rctee_check_memory_access_rights`系统调用
2. **第二优先级**：实现Trusty分配器感知的堆检查函数
3. **第三优先级**：实现TA参数冲突检查和内存类型检测

## 8. 总结

### 8.1 方案优势
1. **完全等价于OP-TEE**：通过系统调用提供相同的权限查询能力
2. **严格符合GP标准**：满足所有安全保证和检查要求
3. **保持架构一致性**：使用trusty标准的系统调用机制
4. **渐进式实现**：可以分阶段实现，逐步完善功能

### 8.2 实现优先级
1. **第一优先级**：实现`_rctee_check_memory_access_rights`系统调用
2. **第二优先级**：实现Trusty分配器感知的堆检查函数
3. **第三优先级**：完善TA参数冲突检查和内存类型检测

这个系统调用扩展方案是实现GP标准兼容的最佳选择，能够提供与OP-TEE完全等价的功能！
