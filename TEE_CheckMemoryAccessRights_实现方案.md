# TEE_CheckMemoryAccessRights系统调用扩展方案

## 1. 用户空间实现 (lib/libutee/tee_api.c)

```c
/**
 * 检查内存访问权限的辅助函数 - 检查与TA参数的重叠
 */
static TEE_Result check_mem_access_rights_params(uint32_t flags, void *buf, size_t len)
{
    size_t n = 0;
    
    for (n = 0; n < TEE_NUM_PARAMS; n++) {
        uint32_t f = TEE_MEMORY_ACCESS_ANY_OWNER;
        
        switch (TEE_PARAM_TYPE_GET(ta_param_types, n)) {
        case TEE_PARAM_TYPE_MEMREF_OUTPUT:
        case TEE_PARAM_TYPE_MEMREF_INOUT:
            f |= TEE_MEMORY_ACCESS_WRITE;
            fallthrough;
        case TEE_PARAM_TYPE_MEMREF_INPUT:
            f |= TEE_MEMORY_ACCESS_READ;
            if (bufs_intersect(buf, len,
                              ta_params[n].memref.buffer,
                              ta_params[n].memref.size)) {
                if ((flags & f) != flags)
                    return TEE_ERROR_ACCESS_DENIED;
            }
            break;
        default:
            break;
        }
    }
    
    return TEE_SUCCESS;
}

/**
 * 检查缓冲区是否重叠
 */
static bool bufs_intersect(void *buf1, size_t sz1, void *buf2, size_t sz2)
{
    vaddr_t b1 = (vaddr_t)buf1;
    vaddr_t b2 = (vaddr_t)buf2;
    vaddr_t e1 = b1 + sz1 - 1;
    vaddr_t e2 = b2 + sz2 - 1;
    
    if (!sz1 || !sz2)
        return false;
    
    if (e1 < b2 || e2 < b1)
        return false;
    
    return true;
}

/**
 * GP标准TEE_CheckMemoryAccessRights实现
 */
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size)
{
    uint32_t flags = accessFlags;
    
    /* GP标准：size为0时直接返回成功 */
    if (!size)
        return TEE_SUCCESS;
    
    /*
     * 第一步：通过系统调用检查内存映射权限
     * 这是核心的MMU权限检查，由内核完成
     */
    if (_utee_check_memory_access_rights(accessFlags, buffer, size))
        return TEE_ERROR_ACCESS_DENIED;
    
    /*
     * 第二步：检查与TA参数的权限兼容性
     * 清除扩展标志，只保留基本访问标志
     */
    flags &= TEE_MEMORY_ACCESS_READ | TEE_MEMORY_ACCESS_WRITE |
             TEE_MEMORY_ACCESS_ANY_OWNER;
    if (check_mem_access_rights_params(flags, buffer, size))
        return TEE_ERROR_ACCESS_DENIED;
    
    /*
     * 第三步：检查堆内存重叠（如果适用）
     * 防止TA暴露私有堆内存
     */
    if (malloc_buffer_overlaps_heap(buffer, size) &&
        !malloc_buffer_is_within_allocated(buffer, size))
        return TEE_ERROR_ACCESS_DENIED;
    
    return TEE_SUCCESS;
}
```

## 2. 系统调用接口定义 (include/user/utee_syscalls.h)

```c
/**
 * 内存访问权限检查系统调用
 * @flags: 访问标志 (TEE_MEMORY_ACCESS_*)
 * @buf: 要检查的缓冲区地址
 * @len: 缓冲区长度
 * @return: TEE_SUCCESS 或 TEE_ERROR_ACCESS_DENIED
 */
TEE_Result _utee_check_memory_access_rights(uint32_t flags, const void *buf, size_t len);
```

## 3. 系统调用号定义 (include/user/utee_syscall_numbers.h)

```c
#define UTEE_SYSCALL_CHECK_MEMORY_ACCESS_RIGHTS    0x1A
```

## 4. 系统调用实现 (kernel/generic_ta_service.c)

```c
/**
 * 系统调用：检查内存访问权限
 */
static long sys_check_memory_access_rights(uint32_t flags, user_addr_t buf_addr, size_t len)
{
    TEE_Result res;
    void *buf = (void *)buf_addr;
    
    /* 参数验证 */
    if (!len)
        return TEE_SUCCESS;
    
    if (!buf)
        return TEE_ERROR_BAD_PARAMETERS;
    
    /* 检查地址溢出 */
    if (buf_addr + len < buf_addr)
        return TEE_ERROR_BAD_PARAMETERS;
    
    /* 调用内核内存权限检查函数 */
    res = trusty_check_memory_access_rights(flags, buf, len);
    
    return res;
}
```

## 5. 内核内存权限检查实现 (kernel/trusty_memory_check.c)

```c
/**
 * Trusty内核内存访问权限检查实现
 */
TEE_Result trusty_check_memory_access_rights(uint32_t flags, void *buffer, size_t size)
{
    vaddr_t addr = (vaddr_t)buffer;
    vaddr_t end_addr;
    size_t check_size = PAGE_SIZE;  /* 按页检查 */
    
    /* 检查地址溢出 */
    if (addr + size < addr)
        return TEE_ERROR_ACCESS_DENIED;
    
    end_addr = addr + size;
    
    /* 逐页检查内存权限 */
    for (vaddr_t va = ROUND_DOWN(addr, PAGE_SIZE); 
         va < end_addr; 
         va += check_size) {
        
        uint32_t mmu_flags;
        TEE_Result res;
        
        /* 获取MMU权限属性 */
        res = get_memory_attributes(va, &mmu_flags);
        if (res != TEE_SUCCESS)
            return TEE_ERROR_ACCESS_DENIED;
        
        /* 检查读权限 */
        if ((flags & TEE_MEMORY_ACCESS_READ) && 
            !(mmu_flags & MMU_FLAG_PERM_USER_READ))
            return TEE_ERROR_ACCESS_DENIED;
        
        /* 检查写权限 */
        if ((flags & TEE_MEMORY_ACCESS_WRITE) && 
            !(mmu_flags & MMU_FLAG_PERM_USER_WRITE))
            return TEE_ERROR_ACCESS_DENIED;
        
        /* 检查所有者权限 */
        if (!(flags & TEE_MEMORY_ACCESS_ANY_OWNER)) {
            if (!is_ta_private_memory(va))
                return TEE_ERROR_ACCESS_DENIED;
        }
    }
    
    return TEE_SUCCESS;
}

/**
 * 获取内存属性的内核函数
 */
static TEE_Result get_memory_attributes(vaddr_t va, uint32_t *flags)
{
    /* 使用现有的Trusty MMU查询接口 */
    uint arch_mmu_flags;
    int ret;

    ret = arch_mmu_query(&current_aspace->arch_aspace, va, NULL, &arch_mmu_flags);
    if (ret < 0)
        return TEE_ERROR_ACCESS_DENIED;

    /* 转换Trusty MMU标志到TEE标志 */
    *flags = 0;
    if (arch_mmu_flags & ARCH_MMU_FLAG_PERM_USER)
        *flags |= MMU_FLAG_PERM_USER_READ;
    if (arch_mmu_flags & ARCH_MMU_FLAG_PERM_RO)
        *flags &= ~MMU_FLAG_PERM_USER_WRITE;
    else if (arch_mmu_flags & ARCH_MMU_FLAG_PERM_USER)
        *flags |= MMU_FLAG_PERM_USER_WRITE;

    return TEE_SUCCESS;
}

/**
 * 检查是否为TA私有内存
 */
static bool is_ta_private_memory(vaddr_t va)
{
    /* 检查地址是否在TA私有内存区域 */
    if (va >= TA_PRIVATE_MEM_BASE && va < TA_PRIVATE_MEM_END)
        return true;

    /* 检查是否为TA分配的堆内存 */
    if (is_ta_heap_memory(va))
        return true;

    return false;
}
```

## 6. 系统调用分发表更新 (kernel/syscall_table.c)

```c
[UTEE_SYSCALL_CHECK_MEMORY_ACCESS_RIGHTS] = sys_check_memory_access_rights,
```

## 7. GP标志定义 (include/tee_api_defines.h)

```c
/* GP TEE内存访问标志 */
#define TEE_MEMORY_ACCESS_READ      0x00000001
#define TEE_MEMORY_ACCESS_WRITE     0x00000002
#define TEE_MEMORY_ACCESS_ANY_OWNER 0x00000004

/* Trusty内部MMU标志 */
#define MMU_FLAG_PERM_USER_READ     0x00000001
#define MMU_FLAG_PERM_USER_WRITE    0x00000002
```

## 8. 实现要点总结

### 8.1 架构设计原则
1. **严格的用户/内核分离**：用户空间只能通过系统调用访问内核功能
2. **三层检查机制**：MMU权限检查 + 参数权限检查 + 堆内存检查
3. **GP标准兼容**：完全符合GP TEE Internal Core API规范

### 8.2 关键技术点
1. **系统调用封装**：`_utee_check_memory_access_rights`封装内核MMU查询
2. **权限转换**：Trusty MMU标志与GP TEE标志的转换
3. **内存分类**：区分TA私有内存、共享内存、堆内存
4. **安全检查**：防止TA暴露私有内存给外部

### 8.3 安全保证
1. **不暴露内核接口**：用户空间无法直接调用`arch_mmu_query`
2. **完整性检查**：确保整个缓冲区都满足访问权限要求
3. **所有者验证**：支持TEE_MEMORY_ACCESS_ANY_OWNER标志的语义
4. **参数验证**：防止地址溢出和无效参数

这个方案确保了TEE_CheckMemoryAccessRights的完整GP标准实现，同时维护了Trusty的安全架构，不会将内核接口暴露给用户空间。
```
