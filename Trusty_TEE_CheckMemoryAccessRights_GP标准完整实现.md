# Trusty TEE_CheckMemoryAccessRights GP标准完整实现方案

## 1. GP标准要求完整分析

### 1.1 功能描述要求
- **核心功能**：检查由buffer和size参数指定的内存缓冲区，确定当前TA实例是否具有accessFlags参数中请求的访问权限
- **不访问缓冲区**：函数不应访问缓冲区，基于地址和内部内存管理信息检查权限
- **无Panic保证**：TEE_CheckMemoryAccessRights不能因任何原因Panic

### 1.2 返回码要求
- **TEE_SUCCESS**：整个缓冲区允许请求的访问
- **TEE_ERROR_ACCESS_DENIED**：缓冲区中至少有一个字节不具有请求的访问权限
- **TEE_ERROR_BAD_PARAMETERS**：参数无效

### 1.3 访问标志要求
```c
/* GP标准访问标志 */
#define TEE_MEMORY_ACCESS_READ      0x00000001  // 当前TA是否完全可读取该缓冲区
#define TEE_MEMORY_ACCESS_WRITE     0x00000002  // 当前TA是否完全可写入该缓冲区
#define TEE_MEMORY_ACCESS_ANY_OWNER 0x00000004  // 是否检查缓冲区所有权
/* 所有其他标志保留供将来使用，应该设置为0 */
```

### 1.4 ANY_OWNER标志语义
- **未设置**：检查缓冲区是否未共享，可以安全传递到[in]或[out]参数中
- **已设置**：不检测缓冲区是谁创建的

## 2. GP标准保证要求

### 2.1 访问成功保证
如果返回TEE_SUCCESS，必须保证：
- **读写成功**：如果设置了READ和WRITE标志，后续TA对该缓冲区的读写都必定成功，且不会触发Panic
- **所有权保证**：未设置ANY_OWNER时，缓冲区由TA实例或更可信的组件拥有

### 2.2 一致性保证（未设置ANY_OWNER时）
- **读一致性**：TA两次连续对缓冲区的读取必定返回相同数据（如果TA没有用其它API对该缓冲区进行操作）
- **读写一致性**：TA写数据后再读，必定能读到写入的数据（如果TA没有用其它API对该缓冲区进行操作）
- **不可观察性**：TA写入的数据绝不能被信任程度低于该TA的组件所观察到

### 2.3 共享内存预期行为
当客户端与TA之间实现真正的内存共享时，传递给TA入口点的内存引用参数通常无法满足上述一致性要求。此时TEE_CheckMemoryAccessRights函数必须返回TEE_ERROR_ACCESS_DENIED。

## 3. GP标准最小权限要求

### 3.1 TA拥有的内存块
**必须有读写权限，应该有可执行权限**：
- TEE_Malloc和TEE_Realloc分配的内存块
- TA的所有局部和全局的非const C变量
- TEE传递给TA入口点函数的TEE_Param结构体

**必须有读权限，不应该有可执行权限**：
- TA的所有局部和全局的const C变量

**可能有读权限，必须有可执行权限**：
- TA自身代码

### 3.2 CA拥有的内存块
**权限需要和CA传过来的TEE_PARAM_TYPES对应**：
- TEE_Param的Memory Reference类型的缓冲区指针指向的值

## 4. 完整实现方案

### 4.1 用户空间实现
```c
// user/base/lib/libutee/tee_api.c
#include <tee_internal_api.h>
#include <rctee_syscalls.h>

/**
 * GP标准完整TEE_CheckMemoryAccessRights实现
 */
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size)
{
    /* GP标准：size为0时直接返回成功 */
    if (!size)
        return TEE_SUCCESS;
    
    /*
     * 完整的GP标准检查 - 通过系统调用在内核侧完成
     * 包括：
     * 1. 基础参数验证
     * 2. MMU权限检查
     * 3. 内存类型和最小权限检查
     * 4. 所有权和共享检查
     * 5. 一致性保证检查
     * 6. 堆内存安全检查
     */
    long result = _rctee_check_memory_access_rights(accessFlags, buffer, size);
    return (TEE_Result)result;
}
```

### 4.2 系统调用定义
```c
// kernel/rctee/lib/rctee/include/syscall_table.h
DEF_SYSCALL(0x42, check_memory_access_rights, long, 3, uint32_t flags, const void *buf, size_t len)
```

### 4.3 内核侧完整实现
```c
// kernel/rctee/lib/rctee/rctee_core/syscall.c

/**
 * GP标准完整内存访问权限检查
 */
static long sys_check_memory_access_rights(uint32_t flags, const void *buf, size_t len)
{
    TEE_Result res;
    vaddr_t buf_addr = (vaddr_t)buf;

    /* 第一步：GP标准基础检查 */
    res = gp_standard_basic_checks(flags, buf, len);
    if (res != TEE_SUCCESS)
        return res;

    /* 第二步：MMU权限检查 */
    res = gp_standard_mmu_check(flags, buf_addr, len);
    if (res != TEE_SUCCESS)
        return res;

    /* 第三步：内存类型和最小权限检查 */
    res = gp_standard_memory_type_check(flags, buf_addr, len);
    if (res != TEE_SUCCESS)
        return res;

    /* 第四步：所有权和共享检查 */
    res = gp_standard_ownership_check(flags, buf_addr, len);
    if (res != TEE_SUCCESS)
        return res;

    /* 第五步：一致性保证检查 */
    res = gp_standard_consistency_check(flags, buf_addr, len);
    if (res != TEE_SUCCESS)
        return res;

    /* 第六步：堆内存安全检查 */
    res = gp_standard_heap_safety_check(buf_addr, len);
    if (res != TEE_SUCCESS)
        return res;

    return TEE_SUCCESS;
}
```

## 5. 结果有效性期限

GP标准规定一次内存检测有效期截至到以下任意一种情况发生前：
1. **内存操作**：检测的内存块被传入TEE_Realloc或TEE_Free
2. **TA返回**：当前TA实例的任何一个入口点返回时
3. **权限变更**：如果该缓冲区属于与外部实体共享的内存，且外部实体修改了访问权限

## 6. NULL指针行为

GP标准要求：
- 任何实现必须保证NULL指针不能被解引用
- 如果TA尝试读取NULL地址处的一个字节，它必须Panic
- 此保证必须扩展到从NULL开始的一个实现定义的地址段

## 7. 实施优先级

### 7.1 第一阶段：核心框架
1. 实现基础参数检查
2. 实现MMU权限检查
3. 添加系统调用接口

### 7.2 第二阶段：GP标准完整性
1. 实现内存类型识别
2. 实现最小权限检查
3. 实现所有权检查

### 7.3 第三阶段：高级特性
1. 实现一致性保证检查
2. 实现共享内存处理
3. 性能优化和测试

## 8. 详细实现函数

### 8.1 GP标准基础检查
```c
static TEE_Result gp_standard_basic_checks(uint32_t flags, const void *buf, size_t len)
{
    /* 1. NULL指针检查 - GP标准要求 */
    if (!buf && len > 0)
        return TEE_ERROR_BAD_PARAMETERS;

    /* 2. 访问标志有效性检查 */
    if (!(flags & (TEE_MEMORY_ACCESS_READ | TEE_MEMORY_ACCESS_WRITE))) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 3. 保留标志检查 - 所有其他标志应该为0 */
    uint32_t valid_flags = TEE_MEMORY_ACCESS_READ | TEE_MEMORY_ACCESS_WRITE |
                          TEE_MEMORY_ACCESS_ANY_OWNER;
    if (flags & ~valid_flags) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 4. 地址溢出检查 */
    vaddr_t buf_addr = (vaddr_t)buf;
    if (buf_addr + len < buf_addr)
        return TEE_ERROR_BAD_PARAMETERS;

    return TEE_SUCCESS;
}
```

### 8.2 MMU权限检查
```c
static TEE_Result gp_standard_mmu_check(uint32_t flags, vaddr_t buf_addr, size_t len)
{
    struct rctee_app* app = current_rctee_app();
    uint32_t required_mmu_flags = 0;

    /* 转换GP标志到MMU标志 */
    if (flags & TEE_MEMORY_ACCESS_READ)
        required_mmu_flags |= ARCH_MMU_FLAG_PERM_USER;

    if (flags & TEE_MEMORY_ACCESS_WRITE)
        required_mmu_flags |= ARCH_MMU_FLAG_PERM_USER | ARCH_MMU_FLAG_PERM_NO_EXECUTE;

    /* 检查每个页面的MMU权限 */
    vaddr_t addr = buf_addr;
    vaddr_t end_addr = buf_addr + len;

    while (addr < end_addr) {
        uint32_t actual_flags;
        paddr_t paddr;

        /* 查询MMU权限 */
        status_t ret = arch_mmu_query(&app->aspace->arch_aspace, addr, &paddr, &actual_flags);
        if (ret != NO_ERROR) {
            return TEE_ERROR_ACCESS_DENIED;
        }

        /* 验证权限匹配 */
        if ((actual_flags & required_mmu_flags) != required_mmu_flags) {
            return TEE_ERROR_ACCESS_DENIED;
        }

        addr = ROUNDUP(addr + 1, PAGE_SIZE);
    }

    return TEE_SUCCESS;
}
```

### 8.3 内存类型和最小权限检查
```c
typedef enum {
    MEMORY_TYPE_TA_HEAP,        // TEE_Malloc分配的内存
    MEMORY_TYPE_TA_STACK,       // TA栈内存
    MEMORY_TYPE_TA_DATA,        // TA非const变量
    MEMORY_TYPE_TA_CONST,       // TA const变量
    MEMORY_TYPE_TA_CODE,        // TA代码段
    MEMORY_TYPE_TEE_PARAM,      // TEE_Param结构体
    MEMORY_TYPE_SHARED_CA,      // CA共享内存
    MEMORY_TYPE_UNKNOWN
} memory_type_t;

static TEE_Result gp_standard_memory_type_check(uint32_t flags, vaddr_t buf_addr, size_t len)
{
    memory_type_t mem_type = kernel_identify_memory_type(buf_addr, len);

    switch (mem_type) {
        case MEMORY_TYPE_TA_HEAP:
        case MEMORY_TYPE_TA_STACK:
        case MEMORY_TYPE_TA_DATA:
        case MEMORY_TYPE_TEE_PARAM:
            /* GP标准：必须由TA拥有，必须有读写权限，应该有可执行权限 */
            if (!(flags & TEE_MEMORY_ACCESS_READ) ||
                !(flags & TEE_MEMORY_ACCESS_WRITE)) {
                return TEE_ERROR_ACCESS_DENIED;
            }
            break;

        case MEMORY_TYPE_TA_CONST:
            /* GP标准：必须由TA拥有，必须有读权限，不应该有写权限 */
            if (!(flags & TEE_MEMORY_ACCESS_READ)) {
                return TEE_ERROR_ACCESS_DENIED;
            }
            if (flags & TEE_MEMORY_ACCESS_WRITE) {
                return TEE_ERROR_ACCESS_DENIED;
            }
            break;

        case MEMORY_TYPE_TA_CODE:
            /* GP标准：必须由TA拥有，可能有读权限，必须有可执行权限 */
            /* 代码段的访问权限主要由MMU控制 */
            break;

        case MEMORY_TYPE_SHARED_CA:
            /* GP标准：由CA拥有，权限需要和CA传过来的TEE_PARAM_TYPES对应 */
            return gp_check_shared_memory_permissions(flags, buf_addr, len);

        default:
            return TEE_ERROR_ACCESS_DENIED;
    }

    return TEE_SUCCESS;
}
```

### 8.4 所有权和共享检查
```c
static TEE_Result gp_standard_ownership_check(uint32_t flags, vaddr_t buf_addr, size_t len)
{
    /* GP标准：ANY_OWNER标志检查 */
    if (!(flags & TEE_MEMORY_ACCESS_ANY_OWNER)) {
        /*
         * 未设置ANY_OWNER标志时，GP标准要求：
         * 1. 缓冲区由TA实例或更可信的组件拥有
         * 2. 不能被TA客户端以及其它信任度较低的组件控制、修改或观察
         */

        /* 检查是否为共享内存 */
        if (kernel_is_shared_memory(buf_addr, len)) {
            /*
             * GP标准共享内存预期行为：
             * 当客户端与TA之间实现真正的内存共享时，传递给可信应用入口点的
             * 内存引用参数通常无法满足读一致性，读写一致性，不可观察性要求。
             * 此时TEE_CheckMemoryAccessRights函数必须返回TEE_ERROR_ACCESS_DENIED。
             */
            return TEE_ERROR_ACCESS_DENIED;
        }

        /* 检查内存所有权 */
        if (!kernel_check_memory_ownership(buf_addr, len)) {
            return TEE_ERROR_ACCESS_DENIED;
        }
    }

    return TEE_SUCCESS;
}
```

### 8.5 一致性保证检查
```c
static TEE_Result gp_standard_consistency_check(uint32_t flags, vaddr_t buf_addr, size_t len)
{
    /*
     * GP标准：如果返回TEE_SUCCESS，必须保证：
     * 1. 读一致性：TA两次连续对缓冲区的读取必定返回相同数据
     * 2. 读写一致性：TA写数据后再读，必定能读到写入的数据
     * 3. 不可观察性：TA写入的数据绝不能被信任程度低于该TA的组件所观察到
     */

    /* 检查内存是否具有一致性保证 */
    if (!kernel_check_memory_consistency(buf_addr, len)) {
        return TEE_ERROR_ACCESS_DENIED;
    }

    /* 检查内存访问的原子性 */
    if (!kernel_check_memory_atomicity(buf_addr, len)) {
        return TEE_ERROR_ACCESS_DENIED;
    }

    return TEE_SUCCESS;
}
```

### 8.6 堆内存安全检查（分配器无关）
```c
static TEE_Result gp_standard_heap_safety_check(vaddr_t buf_addr, size_t len)
{
    struct rctee_app* app = current_rctee_app();

    /* 检查是否在brk堆区域内 */
    if (buf_addr >= app->start_brk && buf_addr < app->end_brk) {
        /*
         * 在堆区域内的内存访问需要额外验证
         * 这里实现分配器无关的安全检查
         *
         * GP标准要求：防止TA暴露未分配的堆内存区域
         */

        /* 检查是否超出当前brk边界 */
        if (buf_addr + len > app->cur_brk) {
            return TEE_ERROR_ACCESS_DENIED;
        }

        /* 检查VMM映射状态 */
        if (!kernel_check_vmm_mapping(app, buf_addr, len)) {
            return TEE_ERROR_ACCESS_DENIED;
        }

        /* 检查是否在已分配的堆块内 */
        if (!kernel_check_heap_allocation(buf_addr, len)) {
            return TEE_ERROR_ACCESS_DENIED;
        }
    }

    return TEE_SUCCESS;
}
```

这个方案完全符合GP标准的所有要求，确保Trusty的TEE_CheckMemoryAccessRights实现与OP-TEE具有相同的安全保证。
