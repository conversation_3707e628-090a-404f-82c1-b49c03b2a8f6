# Trusty 堆检查函数实现（精简版）

## 1. 替换的代码部分

### 1.1 原OP-TEE代码
```c
/*
 * 第三步：检查堆内存重叠（如果适用）
 * 防止TA暴露私有堆内存
 */
if (malloc_buffer_overlaps_heap(buffer, size) &&
    !malloc_buffer_is_within_allocated(buffer, size))
    return TEE_ERROR_ACCESS_DENIED;
```

### 1.2 替换为Trusty版本
```c
/*
 * 第三步：检查堆内存重叠（如果适用）
 * 防止TA暴露私有堆内存 - 支持Trusty分配器
 */
if (trusty_buffer_overlaps_heap(buffer, size) &&
    !trusty_buffer_is_within_allocated(buffer, size))
    return TEE_ERROR_ACCESS_DENIED;
```

## 2. 核心函数实现

### 2.1 trusty_buffer_overlaps_heap()
```c
/**
 * 检查缓冲区是否与堆内存重叠 - Trusty分配器感知
 */
static bool trusty_buffer_overlaps_heap(void *buffer, size_t size)
{
    if (!buffer || !size)
        return false;

#ifdef RCTEE_TA_ALLOCATOR_SCUDO
    /* scudo版本 */
    void *heap_base = scudo_get_heap_base();
    size_t heap_size = scudo_get_heap_size();
#else
    /* dlmalloc版本 */
    void *heap_base = dlmalloc_get_heap_base();
    size_t heap_size = dlmalloc_footprint();
#endif

    if (!heap_base || heap_size == 0)
        return false;

    /* 计算重叠 */
    uintptr_t heap_start = (uintptr_t)heap_base;
    uintptr_t heap_end = heap_start + heap_size;
    uintptr_t buf_start = (uintptr_t)buffer;
    uintptr_t buf_end = buf_start + size;

    return (buf_start < heap_end && buf_end > heap_start);
}
```

### 2.2 trusty_buffer_is_within_allocated()
```c
/**
 * 检查缓冲区是否在已分配的堆块内 - Trusty分配器感知
 */
static bool trusty_buffer_is_within_allocated(void *buffer, size_t size)
{
    if (!buffer || !size)
        return false;

#ifdef RCTEE_TA_ALLOCATOR_SCUDO
    /* scudo版本 */
    size_t usable_size = SCUDO_PREFIX(malloc_usable_size)(buffer);
#else
    /* dlmalloc版本 */
    size_t usable_size = dlmalloc_usable_size(buffer);
#endif

    /* 如果usable_size为0，说明不是有效的已分配块 */
    if (usable_size == 0)
        return false;

    /* 检查整个缓冲区是否在已分配块内 */
    return (size <= usable_size);
}
```

## 3. 需要的分配器接口

### 3.1 dlmalloc需要添加
```c
/* 位置：user/base/lib/dlmalloc/dlmalloc.c */
void *dlmalloc_get_heap_base(void)
{
    extern void *dlmalloc_heap_start;  /* 需要暴露的内部变量 */
    return dlmalloc_heap_start;
}
/* dlmalloc_usable_size() - 已存在 */
/* dlmalloc_footprint() - 已存在 */
```

### 3.2 scudo需要添加
```c
/* 位置：user/base/lib/scudo/scudo.c */
void *scudo_get_heap_base(void)
{
    extern void *scudo_heap_start;  /* 需要暴露的内部变量 */
    return scudo_heap_start;
}

size_t scudo_get_heap_size(void)
{
    extern size_t scudo_heap_size;  /* 需要暴露的内部变量 */
    return scudo_heap_size;
}
/* SCUDO_PREFIX(malloc_usable_size)() - 已存在 */
```

## 4. 头文件包含

```c
/* 在tee_api.c中添加 */
#ifdef RCTEE_TA_ALLOCATOR_SCUDO
    #include <lib/scudo.h>
#else
    #include <lib/dlmalloc.h>
#endif
```

## 5. 编译配置

```makefile
# user/base/lib/libutee/rules.mk
ifeq ($(RCTEE_TA_ALLOCATOR),scudo)
    MODULE_CFLAGS += -DRCTEE_TA_ALLOCATOR_SCUDO
    MODULE_DEPS += user/base/lib/scudo
else
    MODULE_CFLAGS += -DRCTEE_TA_ALLOCATOR_DLMALLOC
    MODULE_DEPS += user/base/lib/dlmalloc
endif
```

## 6. 完整的修改位置

```c
// user/base/lib/libutee/tee_api.c

/* 添加头文件包含 */
#ifdef RCTEE_TA_ALLOCATOR_SCUDO
    #include <lib/scudo.h>
#else
    #include <lib/dlmalloc.h>
#endif

/* 添加两个静态函数 */
static bool trusty_buffer_overlaps_heap(void *buffer, size_t size) { ... }
static bool trusty_buffer_is_within_allocated(void *buffer, size_t size) { ... }

/* 在TEE_CheckMemoryAccessRights中替换调用 */
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size)
{
    /* ... 其他检查 ... */
    
    if (trusty_buffer_overlaps_heap(buffer, size) &&
        !trusty_buffer_is_within_allocated(buffer, size))
        return TEE_ERROR_ACCESS_DENIED;
    
    /* ... 继续其他检查 ... */
}
```

这就是全部需要的修改！只替换堆检查部分，保持其他逻辑不变。
