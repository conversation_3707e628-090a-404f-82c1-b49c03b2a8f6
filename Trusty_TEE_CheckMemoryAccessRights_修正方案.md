# Trusty TEE_CheckMemoryAccessRights 修正实现方案

## 问题分析

### 1. 原方案的关键问题

**内存分配器不固定**：
- Trusty通过`RCTEE_TA_ALLOCATOR`编译时选择分配器
- 默认使用dlmalloc，但可配置为scudo
- 不能假设固定使用dlmalloc

**用户/内核边界违反**：
- 堆内存检查涉及内核级别的内存管理
- 用户空间不应直接调用分配器特定函数
- 违反了Trusty的抽象层设计

**编译时配置**：
```makefile
# make/compability.mk
ifeq ($(RCTEE_TA_ALLOCATOR),)
LIB_SRC_DEPS += user/base/lib/dlmalloc  # 默认
else
LIB_SRC_DEPS += user/base/lib/scudo     # 可选
endif
```

## 2. 修正的架构设计

### 2.1 纯内核侧实现

**原则**：
- 所有内存检查都在内核侧完成
- 用户空间只负责调用系统调用
- 内核侧可以安全访问内存管理接口

### 2.2 系统调用接口（不变）

```c
// kernel/rctee/lib/rctee/include/syscall_table.h
DEF_SYSCALL(0x42, check_memory_access_rights, long, 3, uint32_t flags, const void *buf, size_t len)
```

### 2.3 用户空间实现（简化）

```c
// user/base/lib/libutee/tee_api.c
#include <tee_internal_api.h>
#include <rctee_syscalls.h>

/**
 * GP标准TEE_CheckMemoryAccessRights实现
 * 所有检查都通过系统调用在内核侧完成
 */
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size)
{
    /* GP标准：size为0时直接返回成功 */
    if (!size)
        return TEE_SUCCESS;
    
    /*
     * 统一的内存权限检查 - 通过系统调用
     * 内核侧会进行：
     * 1. MMU权限检查
     * 2. 地址范围验证  
     * 3. 堆内存安全检查（分配器无关）
     * 4. 参数兼容性检查
     */
    long result = _rctee_check_memory_access_rights(accessFlags, buffer, size);
    return (TEE_Result)result;
}
```

### 2.4 内核侧完整实现

```c
// kernel/rctee/lib/rctee/rctee_core/syscall.c

/**
 * 系统调用：检查内存访问权限
 */
static long sys_check_memory_access_rights(uint32_t flags, const void *buf, size_t len)
{
    TEE_Result res;
    vaddr_t buf_addr = (vaddr_t)buf;

    /* 参数验证 */
    if (!len)
        return TEE_SUCCESS;

    if (!buf)
        return TEE_ERROR_BAD_PARAMETERS;

    /* 检查地址溢出 */
    if (buf_addr + len < buf_addr)
        return TEE_ERROR_BAD_PARAMETERS;

    /* 第一步：MMU权限检查 */
    res = kernel_check_mmu_permissions(flags, buf_addr, len);
    if (res != TEE_SUCCESS)
        return res;

    /* 第二步：堆内存安全检查（分配器无关） */
    res = kernel_check_heap_safety(buf_addr, len);
    if (res != TEE_SUCCESS)
        return res;

    /* 第三步：参数权限检查 */
    res = kernel_check_access_params(flags, buf_addr, len);
    if (res != TEE_SUCCESS)
        return res;

    return TEE_SUCCESS;
}

/**
 * 内核侧MMU权限检查
 */
static TEE_Result kernel_check_mmu_permissions(uint32_t flags, vaddr_t buf_addr, size_t len)
{
    struct rctee_app* app = current_rctee_app();
    uint32_t required_mmu_flags = 0;

    /* 转换GP标志到MMU标志 */
    if (flags & TEE_MEMORY_ACCESS_READ)
        required_mmu_flags |= ARCH_MMU_FLAG_PERM_USER;
    
    if (flags & TEE_MEMORY_ACCESS_WRITE)
        required_mmu_flags |= ARCH_MMU_FLAG_PERM_USER | ARCH_MMU_FLAG_PERM_NO_EXECUTE;

    /* 检查每个页面的MMU权限 */
    vaddr_t addr = buf_addr;
    vaddr_t end_addr = buf_addr + len;
    
    while (addr < end_addr) {
        uint32_t actual_flags;
        paddr_t paddr;
        
        /* 查询MMU权限 */
        status_t ret = arch_mmu_query(&app->aspace->arch_aspace, addr, &paddr, &actual_flags);
        if (ret != NO_ERROR) {
            return TEE_ERROR_ACCESS_DENIED;
        }
        
        /* 验证权限匹配 */
        if ((actual_flags & required_mmu_flags) != required_mmu_flags) {
            return TEE_ERROR_ACCESS_DENIED;
        }
        
        addr = ROUNDUP(addr + 1, PAGE_SIZE);
    }
    
    return TEE_SUCCESS;
}

/**
 * 内核侧堆内存安全检查（分配器无关）
 */
static TEE_Result kernel_check_heap_safety(vaddr_t buf_addr, size_t len)
{
    struct rctee_app* app = current_rctee_app();
    
    /* 检查是否在brk堆区域内 */
    if (buf_addr >= app->start_brk && buf_addr < app->end_brk) {
        /*
         * 在堆区域内的内存访问需要额外验证
         * 这里实现分配器无关的安全检查
         * 
         * 保守策略：如果在堆区域内，需要确保：
         * 1. 地址在已映射的堆空间内
         * 2. 不会越界到其他内存区域
         */
        
        /* 检查是否超出当前brk边界 */
        if (buf_addr + len > app->cur_brk) {
            return TEE_ERROR_ACCESS_DENIED;
        }
        
        /* 检查VMM映射状态 */
        if (!kernel_check_vmm_mapping(app, buf_addr, len)) {
            return TEE_ERROR_ACCESS_DENIED;
        }
    }
    
    return TEE_SUCCESS;
}

/**
 * 检查VMM映射状态
 */
static bool kernel_check_vmm_mapping(struct rctee_app* app, vaddr_t addr, size_t len)
{
    /* 检查地址范围是否在VMM中正确映射 */
    vaddr_t end_addr = addr + len;
    
    while (addr < end_addr) {
        if (!vmm_is_valid_addr(app->aspace, addr)) {
            return false;
        }
        addr += PAGE_SIZE;
    }
    
    return true;
}

/**
 * 内核侧参数权限检查
 */
static TEE_Result kernel_check_access_params(uint32_t flags, vaddr_t buf_addr, size_t len)
{
    /* 验证标志组合的有效性 */
    if (!(flags & (TEE_MEMORY_ACCESS_READ | TEE_MEMORY_ACCESS_WRITE))) {
        return TEE_ERROR_BAD_PARAMETERS;
    }
    
    /* 检查地址对齐要求（如果需要） */
    /* 检查其他GP标准要求 */
    
    return TEE_SUCCESS;
}
```

## 3. 实现优势

### 3.1 分配器无关
- 不依赖特定的内存分配器（dlmalloc/scudo）
- 使用内核的VMM和brk机制进行检查
- 适用于任何编译时配置

### 3.2 正确的抽象层
- 用户空间只调用系统调用
- 内核侧可以安全访问内存管理接口
- 维护了Trusty的架构边界

### 3.3 安全性保证
- 所有检查都在内核特权级别进行
- 防止用户空间绕过安全检查
- 符合GP标准的安全要求

## 4. 缺失的关键部分

### 4.1 头文件和常量定义

```c
// user/base/lib/libutee/include/tee_internal_api.h
/* GP标准内存访问标志 */
#define TEE_MEMORY_ACCESS_READ      0x00000001
#define TEE_MEMORY_ACCESS_WRITE     0x00000002
#define TEE_MEMORY_ACCESS_ANY_OWNER 0x00000004

/* TEE_CheckMemoryAccessRights函数声明 */
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size);
```

### 4.2 内核侧缺失的函数实现

```c
// kernel/rctee/lib/rctee/rctee_core/syscall.c

/* 需要实现的辅助函数 */
static bool vmm_is_valid_addr(struct vmm_aspace* aspace, vaddr_t addr)
{
    /* 检查地址是否在VMM中有效映射 */
    /* 这个函数可能需要根据Trusty的VMM实现来调整 */
    return true; // 临时实现
}

/* 需要添加到系统调用表中 */
// 在DEF_SYSCALL_TABLE中添加对应的条目
```

### 4.3 GP标准完整性检查

当前方案缺少GP标准要求的一些检查：

```c
static TEE_Result kernel_check_access_params(uint32_t flags, vaddr_t buf_addr, size_t len)
{
    /* 验证标志组合的有效性 */
    if (!(flags & (TEE_MEMORY_ACCESS_READ | TEE_MEMORY_ACCESS_WRITE))) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* GP标准：检查ANY_OWNER标志的语义 */
    if (flags & TEE_MEMORY_ACCESS_ANY_OWNER) {
        /* ANY_OWNER标志的特殊处理逻辑 */
        /* 需要根据GP标准实现具体的所有权检查 */
    }

    /* GP标准：NULL指针处理 */
    if (buf_addr == 0 && len > 0) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* GP标准：内存块一致性检查 */
    /* 确保整个内存块具有一致的访问权限 */

    return TEE_SUCCESS;
}
```

### 4.4 错误处理和返回值映射

```c
/* GP标准错误码定义 */
#define TEE_SUCCESS                 0x00000000
#define TEE_ERROR_BAD_PARAMETERS    0xFFFF0006
#define TEE_ERROR_ACCESS_DENIED     0xFFFF000F

/* 内核错误码到GP错误码的映射 */
static TEE_Result map_kernel_error_to_tee(status_t kernel_err)
{
    switch (kernel_err) {
        case NO_ERROR:
            return TEE_SUCCESS;
        case ERR_INVALID_ARGS:
            return TEE_ERROR_BAD_PARAMETERS;
        case ERR_ACCESS_DENIED:
            return TEE_ERROR_ACCESS_DENIED;
        default:
            return TEE_ERROR_ACCESS_DENIED;
    }
}
```

## 5. 实施步骤详细计划

### 5.1 第一阶段：基础框架
1. **添加系统调用定义**到syscall_table.h
2. **实现基本的sys_check_memory_access_rights**函数
3. **添加GP标准常量定义**
4. **实现用户空间TEE_CheckMemoryAccessRights**函数

### 5.2 第二阶段：核心检查逻辑
1. **实现kernel_check_mmu_permissions**
2. **实现kernel_check_heap_safety**
3. **实现kernel_check_access_params**
4. **添加错误处理和映射**

### 5.3 第三阶段：测试和验证
1. **编写单元测试**验证各种场景
2. **测试不同分配器配置**（dlmalloc/scudo）
3. **验证GP标准兼容性**
4. **性能测试和优化**

### 5.4 第四阶段：集成和文档
1. **与现有Trusty代码集成**
2. **更新构建系统**（./local_build.sh测试）
3. **编写技术文档**
4. **代码审查和优化**

## 6. 潜在问题和解决方案

### 6.1 VMM接口兼容性
- **问题**：`vmm_is_valid_addr`等函数可能不存在
- **解决**：需要查找Trusty实际的VMM接口并适配

### 6.2 MMU标志映射
- **问题**：GP标志到Trusty MMU标志的映射可能不准确
- **解决**：需要详细研究Trusty的MMU标志定义

### 6.3 性能影响
- **问题**：每次调用都进行完整的MMU检查可能影响性能
- **解决**：考虑缓存机制或优化检查策略

这个补充使方案更加完整和可实施。
