# Trusty TEE_CheckMemoryAccessRights 修正实现方案

## 问题分析

### 1. 原方案的关键问题

**内存分配器不固定**：
- Trusty通过`RCTEE_TA_ALLOCATOR`编译时选择分配器
- 默认使用dlmalloc，但可配置为scudo
- 不能假设固定使用dlmalloc

**用户/内核边界违反**：
- 堆内存检查涉及内核级别的内存管理
- 用户空间不应直接调用分配器特定函数
- 违反了Trusty的抽象层设计

**编译时配置**：
```makefile
# make/compability.mk
ifeq ($(RCTEE_TA_ALLOCATOR),)
LIB_SRC_DEPS += user/base/lib/dlmalloc  # 默认
else
LIB_SRC_DEPS += user/base/lib/scudo     # 可选
endif
```

## 2. 修正的架构设计

### 2.1 纯内核侧实现

**原则**：
- 所有内存检查都在内核侧完成
- 用户空间只负责调用系统调用
- 内核侧可以安全访问内存管理接口

### 2.2 系统调用接口（不变）

```c
// kernel/rctee/lib/rctee/include/syscall_table.h
DEF_SYSCALL(0x42, check_memory_access_rights, long, 3, uint32_t flags, const void *buf, size_t len)
```

### 2.3 用户空间实现（简化）

```c
// user/base/lib/libutee/tee_api.c
#include <tee_internal_api.h>
#include <rctee_syscalls.h>

/**
 * GP标准TEE_CheckMemoryAccessRights实现
 * 所有检查都通过系统调用在内核侧完成
 */
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size)
{
    /* GP标准：size为0时直接返回成功 */
    if (!size)
        return TEE_SUCCESS;
    
    /*
     * 统一的内存权限检查 - 通过系统调用
     * 内核侧会进行：
     * 1. MMU权限检查
     * 2. 地址范围验证  
     * 3. 堆内存安全检查（分配器无关）
     * 4. 参数兼容性检查
     */
    long result = _rctee_check_memory_access_rights(accessFlags, buffer, size);
    return (TEE_Result)result;
}
```

### 2.4 内核侧完整实现

```c
// kernel/rctee/lib/rctee/rctee_core/syscall.c

/**
 * 系统调用：检查内存访问权限
 */
static long sys_check_memory_access_rights(uint32_t flags, const void *buf, size_t len)
{
    TEE_Result res;
    vaddr_t buf_addr = (vaddr_t)buf;

    /* 参数验证 */
    if (!len)
        return TEE_SUCCESS;

    if (!buf)
        return TEE_ERROR_BAD_PARAMETERS;

    /* 检查地址溢出 */
    if (buf_addr + len < buf_addr)
        return TEE_ERROR_BAD_PARAMETERS;

    /* 第一步：MMU权限检查 */
    res = kernel_check_mmu_permissions(flags, buf_addr, len);
    if (res != TEE_SUCCESS)
        return res;

    /* 第二步：堆内存安全检查（分配器无关） */
    res = kernel_check_heap_safety(buf_addr, len);
    if (res != TEE_SUCCESS)
        return res;

    /* 第三步：参数权限检查 */
    res = kernel_check_access_params(flags, buf_addr, len);
    if (res != TEE_SUCCESS)
        return res;

    return TEE_SUCCESS;
}

/**
 * 内核侧MMU权限检查
 */
static TEE_Result kernel_check_mmu_permissions(uint32_t flags, vaddr_t buf_addr, size_t len)
{
    struct rctee_app* app = current_rctee_app();
    uint32_t required_mmu_flags = 0;

    /* 转换GP标志到MMU标志 */
    if (flags & TEE_MEMORY_ACCESS_READ)
        required_mmu_flags |= ARCH_MMU_FLAG_PERM_USER;
    
    if (flags & TEE_MEMORY_ACCESS_WRITE)
        required_mmu_flags |= ARCH_MMU_FLAG_PERM_USER | ARCH_MMU_FLAG_PERM_NO_EXECUTE;

    /* 检查每个页面的MMU权限 */
    vaddr_t addr = buf_addr;
    vaddr_t end_addr = buf_addr + len;
    
    while (addr < end_addr) {
        uint32_t actual_flags;
        paddr_t paddr;
        
        /* 查询MMU权限 */
        status_t ret = arch_mmu_query(&app->aspace->arch_aspace, addr, &paddr, &actual_flags);
        if (ret != NO_ERROR) {
            return TEE_ERROR_ACCESS_DENIED;
        }
        
        /* 验证权限匹配 */
        if ((actual_flags & required_mmu_flags) != required_mmu_flags) {
            return TEE_ERROR_ACCESS_DENIED;
        }
        
        addr = ROUNDUP(addr + 1, PAGE_SIZE);
    }
    
    return TEE_SUCCESS;
}

/**
 * 内核侧堆内存安全检查（分配器无关）
 */
static TEE_Result kernel_check_heap_safety(vaddr_t buf_addr, size_t len)
{
    struct rctee_app* app = current_rctee_app();
    
    /* 检查是否在brk堆区域内 */
    if (buf_addr >= app->start_brk && buf_addr < app->end_brk) {
        /*
         * 在堆区域内的内存访问需要额外验证
         * 这里实现分配器无关的安全检查
         * 
         * 保守策略：如果在堆区域内，需要确保：
         * 1. 地址在已映射的堆空间内
         * 2. 不会越界到其他内存区域
         */
        
        /* 检查是否超出当前brk边界 */
        if (buf_addr + len > app->cur_brk) {
            return TEE_ERROR_ACCESS_DENIED;
        }
        
        /* 检查VMM映射状态 */
        if (!kernel_check_vmm_mapping(app, buf_addr, len)) {
            return TEE_ERROR_ACCESS_DENIED;
        }
    }
    
    return TEE_SUCCESS;
}

/**
 * 检查VMM映射状态
 */
static bool kernel_check_vmm_mapping(struct rctee_app* app, vaddr_t addr, size_t len)
{
    /* 检查地址范围是否在VMM中正确映射 */
    vaddr_t end_addr = addr + len;
    
    while (addr < end_addr) {
        if (!vmm_is_valid_addr(app->aspace, addr)) {
            return false;
        }
        addr += PAGE_SIZE;
    }
    
    return true;
}

/**
 * 内核侧参数权限检查
 */
static TEE_Result kernel_check_access_params(uint32_t flags, vaddr_t buf_addr, size_t len)
{
    /* 验证标志组合的有效性 */
    if (!(flags & (TEE_MEMORY_ACCESS_READ | TEE_MEMORY_ACCESS_WRITE))) {
        return TEE_ERROR_BAD_PARAMETERS;
    }
    
    /* 检查地址对齐要求（如果需要） */
    /* 检查其他GP标准要求 */
    
    return TEE_SUCCESS;
}
```

## 3. 实现优势

### 3.1 分配器无关
- 不依赖特定的内存分配器（dlmalloc/scudo）
- 使用内核的VMM和brk机制进行检查
- 适用于任何编译时配置

### 3.2 正确的抽象层
- 用户空间只调用系统调用
- 内核侧可以安全访问内存管理接口
- 维护了Trusty的架构边界

### 3.3 安全性保证
- 所有检查都在内核特权级别进行
- 防止用户空间绕过安全检查
- 符合GP标准的安全要求

## 4. 下一步实施

1. **移除用户空间的分配器特定代码**
2. **在内核侧实现完整的内存检查逻辑**
3. **测试不同分配器配置下的兼容性**
4. **验证与现有Trusty内存管理的集成**

这个修正方案解决了原方案的所有关键问题，提供了一个架构正确、分配器无关的实现。
