# Now used only by Trusty
LOCAL_ADDITIONAL_DEPENDENCIES += $(LOCAL_PATH)/sources.mk
include $(LOCAL_PATH)/sources.mk

LOCAL_CFLAGS += -I$(LOCAL_PATH)/include -I$(LOCAL_PATH)/crypto -I$(LOCAL_PATH) \
	-DOPENSSL_NO_ASM \
	-DOPENSSL_NO_SHARED \
	-DOPENSSL_STATIC_ARMCAP \
	-DOPENSSL_SMALL_FOOTPRINT \
	-DTRUSTY_USERSPACE \
	-Wno-unused-parameter \
	-Wno-sign-compare \
	-Wno-unused-function

LOCAL_ASFLAGS += -I$(LOCAL_PATH)/include -I$(LOCAL_PATH)/crypto -Wno-unused-parameter
LOCAL_SRC_FILES += $(tongsuo_sources)
