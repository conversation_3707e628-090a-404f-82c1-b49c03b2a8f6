/*
 * Generated by util/mkerr.pl DO NOT EDIT
 * Copyright 1995-2023 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#ifndef OPENSSL_ZKPERR_H
# define OPENSSL_ZKPERR_H
# pragma once

# include <openssl/configuration.h>
# include <openssl/symhacks.h>
# include <openssl/cryptoerr_legacy.h>



/*
 * ZKP reason codes.
 */
# define ZKP_BP_R_EXCEEDS_GENS_CAPACITY                   106
# define ZKP_BP_R_EXCEEDS_MAX_AGG_NUM                     100
# define ZKP_BP_R_EXCEEDS_MAX_BITS                        101
# define ZKP_BP_R_EXCEEDS_PARTY_CAPACITY                  104
# define ZKP_BP_R_EXCEEDS_PP_CAPACITY                     102
# define ZKP_BP_R_R1CS_CONSTRAINT_EXPRESSION_FORMAT_ERROR 109
# define ZKP_BP_R_R1CS_CONSTRAINT_EXPRESSION_NO_VAR       110
# define ZKP_BP_R_R1CS_CONSTRAINT_EXPRESSION_PROCESS_ERROR 111
# define ZKP_BP_R_R1CS_CONSTRAINT_EXPRESSION_VAR_NOT_FOUND 112
# define ZKP_BP_R_R1CS_CONSTRAINT_EXPRESSION_VAR_TOO_LONG 113
# define ZKP_BP_R_TRANSCRIPT_INIT_FAILED                  103
# define ZKP_BP_R_VARIABLE_DUPLICATED                     107
# define ZKP_BP_R_VARIABLE_NAME_TOO_LONG                  108
# define ZKP_BP_R_WITNESS_INVALID                         105
# define ZKP_NIZK_R_TRANSCRIPT_INIT_FAILED                100
# define ZKP_R_BULLETPROOFS_RANGE_PROVE_FAILED            101
# define ZKP_R_BULLETPROOFS_RANGE_VERIFY_FAILED           102
# define ZKP_R_NIZK_PLAINTEXT_KNOWLEDGE_PROVE_FAILED      103
# define ZKP_R_NIZK_PLAINTEXT_KNOWLEDGE_VERIFY_FAILED     104
# define ZKP_R_RANGE_PROVE_FAILED                         105
# define ZKP_R_RANGE_VERIFY_FAILED                        106
# define ZKP_R_TRANSCRIPT_INIT_FAILED                     100

#endif
