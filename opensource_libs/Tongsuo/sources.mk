# Copyright (c) 2024, Tongsuo Project
#
# Auto-generated sources.mk for Tongsuo integration with trusty-tee
# Generated by generate_sources.py. Do not edit manually.

crypto_sources := \
  $(LOCAL_DIR)/crypto/aes/aes_cbc.c\
  $(LOCAL_DIR)/crypto/aes/aes_cfb.c\
  $(LOCAL_DIR)/crypto/aes/aes_core.c\
  $(LOCAL_DIR)/crypto/aes/aes_ecb.c\
  $(LOCAL_DIR)/crypto/aes/aes_ige.c\
  $(LOCAL_DIR)/crypto/aes/aes_misc.c\
  $(LOCAL_DIR)/crypto/aes/aes_ofb.c\
  $(LOCAL_DIR)/crypto/aes/aes_wrap.c\
  $(LOCAL_DIR)/crypto/aes/aes_x86core.c\
  $(LOCAL_DIR)/crypto/armcap.c\
  $(LOCAL_DIR)/crypto/asn1/a_bitstr.c\
  $(LOCAL_DIR)/crypto/asn1/a_d2i_fp.c\
  $(LOCAL_DIR)/crypto/asn1/a_digest.c\
  $(LOCAL_DIR)/crypto/asn1/a_dup.c\
  $(LOCAL_DIR)/crypto/asn1/a_gentm.c\
  $(LOCAL_DIR)/crypto/asn1/a_i2d_fp.c\
  $(LOCAL_DIR)/crypto/asn1/a_int.c\
  $(LOCAL_DIR)/crypto/asn1/a_mbstr.c\
  $(LOCAL_DIR)/crypto/asn1/a_object.c\
  $(LOCAL_DIR)/crypto/asn1/a_octet.c\
  $(LOCAL_DIR)/crypto/asn1/a_print.c\
  $(LOCAL_DIR)/crypto/asn1/a_sign.c\
  $(LOCAL_DIR)/crypto/asn1/a_strex.c\
  $(LOCAL_DIR)/crypto/asn1/a_strnid.c\
  $(LOCAL_DIR)/crypto/asn1/a_time.c\
  $(LOCAL_DIR)/crypto/asn1/a_type.c\
  $(LOCAL_DIR)/crypto/asn1/a_utctm.c\
  $(LOCAL_DIR)/crypto/asn1/a_utf8.c\
  $(LOCAL_DIR)/crypto/asn1/a_verify.c\
  $(LOCAL_DIR)/crypto/asn1/ameth_lib.c\
  $(LOCAL_DIR)/crypto/asn1/asn1_err.c\
  $(LOCAL_DIR)/crypto/asn1/asn1_gen.c\
  $(LOCAL_DIR)/crypto/asn1/asn1_item_list.c\
  $(LOCAL_DIR)/crypto/asn1/asn1_lib.c\
  $(LOCAL_DIR)/crypto/asn1/asn1_parse.c\
  $(LOCAL_DIR)/crypto/asn1/asn_mime.c\
  $(LOCAL_DIR)/crypto/asn1/asn_moid.c\
  $(LOCAL_DIR)/crypto/asn1/asn_mstbl.c\
  $(LOCAL_DIR)/crypto/asn1/asn_pack.c\
  $(LOCAL_DIR)/crypto/asn1/bio_asn1.c\
  $(LOCAL_DIR)/crypto/asn1/bio_ndef.c\
  $(LOCAL_DIR)/crypto/asn1/d2i_param.c\
  $(LOCAL_DIR)/crypto/asn1/d2i_pr.c\
  $(LOCAL_DIR)/crypto/asn1/d2i_pu.c\
  $(LOCAL_DIR)/crypto/asn1/evp_asn1.c\
  $(LOCAL_DIR)/crypto/asn1/f_int.c\
  $(LOCAL_DIR)/crypto/asn1/f_string.c\
  $(LOCAL_DIR)/crypto/asn1/i2d_evp.c\
  $(LOCAL_DIR)/crypto/asn1/n_pkey.c\
  $(LOCAL_DIR)/crypto/asn1/nsseq.c\
  $(LOCAL_DIR)/crypto/asn1/p5_pbe.c\
  $(LOCAL_DIR)/crypto/asn1/p5_pbev2.c\
  $(LOCAL_DIR)/crypto/asn1/p5_scrypt.c\
  $(LOCAL_DIR)/crypto/asn1/p8_pkey.c\
  $(LOCAL_DIR)/crypto/asn1/t_bitst.c\
  $(LOCAL_DIR)/crypto/asn1/t_pkey.c\
  $(LOCAL_DIR)/crypto/asn1/t_spki.c\
  $(LOCAL_DIR)/crypto/asn1/tasn_dec.c\
  $(LOCAL_DIR)/crypto/asn1/tasn_enc.c\
  $(LOCAL_DIR)/crypto/asn1/tasn_fre.c\
  $(LOCAL_DIR)/crypto/asn1/tasn_new.c\
  $(LOCAL_DIR)/crypto/asn1/tasn_prn.c\
  $(LOCAL_DIR)/crypto/asn1/tasn_scn.c\
  $(LOCAL_DIR)/crypto/asn1/tasn_typ.c\
  $(LOCAL_DIR)/crypto/asn1/tasn_utl.c\
  $(LOCAL_DIR)/crypto/asn1/x_algor.c\
  $(LOCAL_DIR)/crypto/asn1/x_bignum.c\
  $(LOCAL_DIR)/crypto/asn1/x_info.c\
  $(LOCAL_DIR)/crypto/asn1/x_int64.c\
  $(LOCAL_DIR)/crypto/asn1/x_long.c\
  $(LOCAL_DIR)/crypto/asn1/x_pkey.c\
  $(LOCAL_DIR)/crypto/asn1/x_sig.c\
  $(LOCAL_DIR)/crypto/asn1/x_spki.c\
  $(LOCAL_DIR)/crypto/asn1/x_val.c\
  $(LOCAL_DIR)/crypto/asn1_dsa.c\
  $(LOCAL_DIR)/crypto/async/arch/async_null.c\
  $(LOCAL_DIR)/crypto/async/arch/async_posix.c\
  $(LOCAL_DIR)/crypto/async/arch/async_win.c\
  $(LOCAL_DIR)/crypto/async/async.c\
  $(LOCAL_DIR)/crypto/async/async_err.c\
  $(LOCAL_DIR)/crypto/async/async_wait.c\
  $(LOCAL_DIR)/crypto/bio/bf_buff.c\
  $(LOCAL_DIR)/crypto/bio/bf_lbuf.c\
  $(LOCAL_DIR)/crypto/bio/bf_nbio.c\
  $(LOCAL_DIR)/crypto/bio/bf_null.c\
  $(LOCAL_DIR)/crypto/bio/bf_prefix.c\
  $(LOCAL_DIR)/crypto/bio/bf_readbuff.c\
  $(LOCAL_DIR)/crypto/bio/bio_addr.c\
  $(LOCAL_DIR)/crypto/bio/bio_cb.c\
  $(LOCAL_DIR)/crypto/bio/bio_dump.c\
  $(LOCAL_DIR)/crypto/bio/bio_err.c\
  $(LOCAL_DIR)/crypto/bio/bio_lib.c\
  $(LOCAL_DIR)/crypto/bio/bio_meth.c\
  $(LOCAL_DIR)/crypto/bio/bio_print.c\
  $(LOCAL_DIR)/crypto/bio/bio_sock.c\
  $(LOCAL_DIR)/crypto/bio/bio_sock2.c\
  $(LOCAL_DIR)/crypto/bio/bss_acpt.c\
  $(LOCAL_DIR)/crypto/bio/bss_bio.c\
  $(LOCAL_DIR)/crypto/bio/bss_conn.c\
  $(LOCAL_DIR)/crypto/bio/bss_core.c\
  $(LOCAL_DIR)/crypto/bio/bss_dgram.c\
  $(LOCAL_DIR)/crypto/bio/bss_fd.c\
  $(LOCAL_DIR)/crypto/bio/bss_file.c\
  $(LOCAL_DIR)/crypto/bio/bss_log.c\
  $(LOCAL_DIR)/crypto/bio/bss_mem.c\
  $(LOCAL_DIR)/crypto/bio/bss_null.c\
  $(LOCAL_DIR)/crypto/bio/bss_sock.c\
  $(LOCAL_DIR)/crypto/bio/ossl_core_bio.c\
  $(LOCAL_DIR)/crypto/bn/asm/x86_64-gcc.c\
  $(LOCAL_DIR)/crypto/bn/bn_add.c\
  $(LOCAL_DIR)/crypto/bn/bn_asm.c\
  $(LOCAL_DIR)/crypto/bn/bn_blind.c\
  $(LOCAL_DIR)/crypto/bn/bn_const.c\
  $(LOCAL_DIR)/crypto/bn/bn_conv.c\
  $(LOCAL_DIR)/crypto/bn/bn_ctx.c\
  $(LOCAL_DIR)/crypto/bn/bn_depr.c\
  $(LOCAL_DIR)/crypto/bn/bn_dh.c\
  $(LOCAL_DIR)/crypto/bn/bn_div.c\
  $(LOCAL_DIR)/crypto/bn/bn_err.c\
  $(LOCAL_DIR)/crypto/bn/bn_exp.c\
  $(LOCAL_DIR)/crypto/bn/bn_exp2.c\
  $(LOCAL_DIR)/crypto/bn/bn_gcd.c\
  $(LOCAL_DIR)/crypto/bn/bn_gf2m.c\
  $(LOCAL_DIR)/crypto/bn/bn_intern.c\
  $(LOCAL_DIR)/crypto/bn/bn_kron.c\
  $(LOCAL_DIR)/crypto/bn/bn_lib.c\
  $(LOCAL_DIR)/crypto/bn/bn_meth.c\
  $(LOCAL_DIR)/crypto/bn/bn_mod.c\
  $(LOCAL_DIR)/crypto/bn/bn_mont.c\
  $(LOCAL_DIR)/crypto/bn/bn_mpi.c\
  $(LOCAL_DIR)/crypto/bn/bn_mul.c\
  $(LOCAL_DIR)/crypto/bn/bn_nist.c\
  $(LOCAL_DIR)/crypto/bn/bn_ppc.c\
  $(LOCAL_DIR)/crypto/bn/bn_prime.c\
  $(LOCAL_DIR)/crypto/bn/bn_print.c\
  $(LOCAL_DIR)/crypto/bn/bn_rand.c\
  $(LOCAL_DIR)/crypto/bn/bn_recp.c\
  $(LOCAL_DIR)/crypto/bn/bn_rsa_fips186_4.c\
  $(LOCAL_DIR)/crypto/bn/bn_shift.c\
  $(LOCAL_DIR)/crypto/bn/bn_sm2.c\
  $(LOCAL_DIR)/crypto/bn/bn_sqr.c\
  $(LOCAL_DIR)/crypto/bn/bn_sqrt.c\
  $(LOCAL_DIR)/crypto/bn/bn_srp.c\
  $(LOCAL_DIR)/crypto/bn/bn_word.c\
  $(LOCAL_DIR)/crypto/bn/bn_x931p.c\
  $(LOCAL_DIR)/crypto/bn/rsaz_exp.c\
  $(LOCAL_DIR)/crypto/bn/rsaz_exp_x2.c\
  $(LOCAL_DIR)/crypto/bsearch.c\
  $(LOCAL_DIR)/crypto/buffer/buf_err.c\
  $(LOCAL_DIR)/crypto/buffer/buffer.c\
  $(LOCAL_DIR)/crypto/chacha/chacha_enc.c\
  $(LOCAL_DIR)/crypto/chacha/chacha_ppc.c\
  $(LOCAL_DIR)/crypto/cmac/cmac.c\
  $(LOCAL_DIR)/crypto/cmp/cmp_asn.c\
  $(LOCAL_DIR)/crypto/cmp/cmp_client.c\
  $(LOCAL_DIR)/crypto/cmp/cmp_ctx.c\
  $(LOCAL_DIR)/crypto/cmp/cmp_err.c\
  $(LOCAL_DIR)/crypto/cmp/cmp_hdr.c\
  $(LOCAL_DIR)/crypto/cmp/cmp_http.c\
  $(LOCAL_DIR)/crypto/cmp/cmp_msg.c\
  $(LOCAL_DIR)/crypto/cmp/cmp_protect.c\
  $(LOCAL_DIR)/crypto/cmp/cmp_server.c\
  $(LOCAL_DIR)/crypto/cmp/cmp_status.c\
  $(LOCAL_DIR)/crypto/cmp/cmp_util.c\
  $(LOCAL_DIR)/crypto/cmp/cmp_vfy.c\
  $(LOCAL_DIR)/crypto/cms/cms_asn1.c\
  $(LOCAL_DIR)/crypto/cms/cms_att.c\
  $(LOCAL_DIR)/crypto/cms/cms_cd.c\
  $(LOCAL_DIR)/crypto/cms/cms_dd.c\
  $(LOCAL_DIR)/crypto/cms/cms_dh.c\
  $(LOCAL_DIR)/crypto/cms/cms_ec.c\
  $(LOCAL_DIR)/crypto/cms/cms_enc.c\
  $(LOCAL_DIR)/crypto/cms/cms_env.c\
  $(LOCAL_DIR)/crypto/cms/cms_err.c\
  $(LOCAL_DIR)/crypto/cms/cms_ess.c\
  $(LOCAL_DIR)/crypto/cms/cms_io.c\
  $(LOCAL_DIR)/crypto/cms/cms_kari.c\
  $(LOCAL_DIR)/crypto/cms/cms_lib.c\
  $(LOCAL_DIR)/crypto/cms/cms_pwri.c\
  $(LOCAL_DIR)/crypto/cms/cms_rsa.c\
  $(LOCAL_DIR)/crypto/cms/cms_sd.c\
  $(LOCAL_DIR)/crypto/cms/cms_smime.c\
  $(LOCAL_DIR)/crypto/comp/c_zlib.c\
  $(LOCAL_DIR)/crypto/comp/comp_err.c\
  $(LOCAL_DIR)/crypto/comp/comp_lib.c\
  $(LOCAL_DIR)/crypto/conf/conf_api.c\
  $(LOCAL_DIR)/crypto/conf/conf_def.c\
  $(LOCAL_DIR)/crypto/conf/conf_err.c\
  $(LOCAL_DIR)/crypto/conf/conf_lib.c\
  $(LOCAL_DIR)/crypto/conf/conf_mall.c\
  $(LOCAL_DIR)/crypto/conf/conf_mod.c\
  $(LOCAL_DIR)/crypto/conf/conf_sap.c\
  $(LOCAL_DIR)/crypto/conf/conf_ssl.c\
  $(LOCAL_DIR)/crypto/context.c\
  $(LOCAL_DIR)/crypto/core_algorithm.c\
  $(LOCAL_DIR)/crypto/core_fetch.c\
  $(LOCAL_DIR)/crypto/core_namemap.c\
  $(LOCAL_DIR)/crypto/cpt_err.c\
  $(LOCAL_DIR)/crypto/cpuid.c\
  $(LOCAL_DIR)/crypto/crmf/crmf_asn.c\
  $(LOCAL_DIR)/crypto/crmf/crmf_err.c\
  $(LOCAL_DIR)/crypto/crmf/crmf_lib.c\
  $(LOCAL_DIR)/crypto/crmf/crmf_pbm.c\
  $(LOCAL_DIR)/crypto/cryptlib.c\
  $(LOCAL_DIR)/crypto/ct/ct_b64.c\
  $(LOCAL_DIR)/crypto/ct/ct_err.c\
  $(LOCAL_DIR)/crypto/ct/ct_log.c\
  $(LOCAL_DIR)/crypto/ct/ct_oct.c\
  $(LOCAL_DIR)/crypto/ct/ct_policy.c\
  $(LOCAL_DIR)/crypto/ct/ct_prn.c\
  $(LOCAL_DIR)/crypto/ct/ct_sct.c\
  $(LOCAL_DIR)/crypto/ct/ct_sct_ctx.c\
  $(LOCAL_DIR)/crypto/ct/ct_vfy.c\
  $(LOCAL_DIR)/crypto/ct/ct_x509v3.c\
  $(LOCAL_DIR)/crypto/ctype.c\
  $(LOCAL_DIR)/crypto/cversion.c\
  $(LOCAL_DIR)/crypto/der_writer.c\
  $(LOCAL_DIR)/crypto/des/cbc_cksm.c\
  $(LOCAL_DIR)/crypto/des/cbc_enc.c\
  $(LOCAL_DIR)/crypto/des/cfb64ede.c\
  $(LOCAL_DIR)/crypto/des/cfb64enc.c\
  $(LOCAL_DIR)/crypto/des/cfb_enc.c\
  $(LOCAL_DIR)/crypto/des/des_enc.c\
  $(LOCAL_DIR)/crypto/des/ecb3_enc.c\
  $(LOCAL_DIR)/crypto/des/ecb_enc.c\
  $(LOCAL_DIR)/crypto/des/fcrypt.c\
  $(LOCAL_DIR)/crypto/des/fcrypt_b.c\
  $(LOCAL_DIR)/crypto/des/ncbc_enc.c\
  $(LOCAL_DIR)/crypto/des/ofb64ede.c\
  $(LOCAL_DIR)/crypto/des/ofb64enc.c\
  $(LOCAL_DIR)/crypto/des/ofb_enc.c\
  $(LOCAL_DIR)/crypto/des/pcbc_enc.c\
  $(LOCAL_DIR)/crypto/des/qud_cksm.c\
  $(LOCAL_DIR)/crypto/des/rand_key.c\
  $(LOCAL_DIR)/crypto/des/set_key.c\
  $(LOCAL_DIR)/crypto/des/str2key.c\
  $(LOCAL_DIR)/crypto/des/xcbc_enc.c\
  $(LOCAL_DIR)/crypto/dh/dh_ameth.c\
  $(LOCAL_DIR)/crypto/dh/dh_asn1.c\
  $(LOCAL_DIR)/crypto/dh/dh_backend.c\
  $(LOCAL_DIR)/crypto/dh/dh_check.c\
  $(LOCAL_DIR)/crypto/dh/dh_depr.c\
  $(LOCAL_DIR)/crypto/dh/dh_err.c\
  $(LOCAL_DIR)/crypto/dh/dh_gen.c\
  $(LOCAL_DIR)/crypto/dh/dh_group_params.c\
  $(LOCAL_DIR)/crypto/dh/dh_kdf.c\
  $(LOCAL_DIR)/crypto/dh/dh_key.c\
  $(LOCAL_DIR)/crypto/dh/dh_lib.c\
  $(LOCAL_DIR)/crypto/dh/dh_meth.c\
  $(LOCAL_DIR)/crypto/dh/dh_pmeth.c\
  $(LOCAL_DIR)/crypto/dh/dh_prn.c\
  $(LOCAL_DIR)/crypto/dh/dh_rfc5114.c\
  $(LOCAL_DIR)/crypto/dllmain.c\
  $(LOCAL_DIR)/crypto/dsa/dsa_ameth.c\
  $(LOCAL_DIR)/crypto/dsa/dsa_asn1.c\
  $(LOCAL_DIR)/crypto/dsa/dsa_backend.c\
  $(LOCAL_DIR)/crypto/dsa/dsa_check.c\
  $(LOCAL_DIR)/crypto/dsa/dsa_depr.c\
  $(LOCAL_DIR)/crypto/dsa/dsa_err.c\
  $(LOCAL_DIR)/crypto/dsa/dsa_gen.c\
  $(LOCAL_DIR)/crypto/dsa/dsa_key.c\
  $(LOCAL_DIR)/crypto/dsa/dsa_lib.c\
  $(LOCAL_DIR)/crypto/dsa/dsa_meth.c\
  $(LOCAL_DIR)/crypto/dsa/dsa_ossl.c\
  $(LOCAL_DIR)/crypto/dsa/dsa_pmeth.c\
  $(LOCAL_DIR)/crypto/dsa/dsa_prn.c\
  $(LOCAL_DIR)/crypto/dsa/dsa_sign.c\
  $(LOCAL_DIR)/crypto/dsa/dsa_vrf.c\
  $(LOCAL_DIR)/crypto/dso/dso_dl.c\
  $(LOCAL_DIR)/crypto/dso/dso_dlfcn.c\
  $(LOCAL_DIR)/crypto/dso/dso_err.c\
  $(LOCAL_DIR)/crypto/dso/dso_lib.c\
  $(LOCAL_DIR)/crypto/dso/dso_openssl.c\
  $(LOCAL_DIR)/crypto/dso/dso_win32.c\
  $(LOCAL_DIR)/crypto/ebcdic.c\
  $(LOCAL_DIR)/crypto/ec/curve25519.c\
  $(LOCAL_DIR)/crypto/ec/curve448/arch_32/f_impl32.c\
  $(LOCAL_DIR)/crypto/ec/curve448/arch_64/f_impl64.c\
  $(LOCAL_DIR)/crypto/ec/curve448/curve448.c\
  $(LOCAL_DIR)/crypto/ec/curve448/curve448_tables.c\
  $(LOCAL_DIR)/crypto/ec/curve448/eddsa.c\
  $(LOCAL_DIR)/crypto/ec/curve448/f_generic.c\
  $(LOCAL_DIR)/crypto/ec/curve448/scalar.c\
  $(LOCAL_DIR)/crypto/ec/ec2_oct.c\
  $(LOCAL_DIR)/crypto/ec/ec2_smpl.c\
  $(LOCAL_DIR)/crypto/ec/ec_ameth.c\
  $(LOCAL_DIR)/crypto/ec/ec_asn1.c\
  $(LOCAL_DIR)/crypto/ec/ec_backend.c\
  $(LOCAL_DIR)/crypto/ec/ec_check.c\
  $(LOCAL_DIR)/crypto/ec/ec_curve.c\
  $(LOCAL_DIR)/crypto/ec/ec_cvt.c\
  $(LOCAL_DIR)/crypto/ec/ec_deprecated.c\
  $(LOCAL_DIR)/crypto/ec/ec_elgamal_crypt.c\
  $(LOCAL_DIR)/crypto/ec/ec_elgamal_dlog.c\
  $(LOCAL_DIR)/crypto/ec/ec_elgamal_encode.c\
  $(LOCAL_DIR)/crypto/ec/ec_err.c\
  $(LOCAL_DIR)/crypto/ec/ec_key.c\
  $(LOCAL_DIR)/crypto/ec/ec_kmeth.c\
  $(LOCAL_DIR)/crypto/ec/ec_lib.c\
  $(LOCAL_DIR)/crypto/ec/ec_mult.c\
  $(LOCAL_DIR)/crypto/ec/ec_oct.c\
  $(LOCAL_DIR)/crypto/ec/ec_pmeth.c\
  $(LOCAL_DIR)/crypto/ec/ec_print.c\
  $(LOCAL_DIR)/crypto/ec/ecdh_kdf.c\
  $(LOCAL_DIR)/crypto/ec/ecdh_ossl.c\
  $(LOCAL_DIR)/crypto/ec/ecdsa_ossl.c\
  $(LOCAL_DIR)/crypto/ec/ecdsa_sign.c\
  $(LOCAL_DIR)/crypto/ec/ecdsa_vrf.c\
  $(LOCAL_DIR)/crypto/ec/eck_prn.c\
  $(LOCAL_DIR)/crypto/ec/ecp_meth.c\
  $(LOCAL_DIR)/crypto/ec/ecp_mont.c\
  $(LOCAL_DIR)/crypto/ec/ecp_nist.c\
  $(LOCAL_DIR)/crypto/ec/ecp_nistp224.c\
  $(LOCAL_DIR)/crypto/ec/ecp_nistp256.c\
  $(LOCAL_DIR)/crypto/ec/ecp_nistp521.c\
  $(LOCAL_DIR)/crypto/ec/ecp_nistputil.c\
  $(LOCAL_DIR)/crypto/ec/ecp_nistz256.c\
  $(LOCAL_DIR)/crypto/ec/ecp_nistz256_table.c\
  $(LOCAL_DIR)/crypto/ec/ecp_oct.c\
  $(LOCAL_DIR)/crypto/ec/ecp_ppc.c\
  $(LOCAL_DIR)/crypto/ec/ecp_s390x_nistp.c\
  $(LOCAL_DIR)/crypto/ec/ecp_sm2p256.c\
  $(LOCAL_DIR)/crypto/ec/ecp_smpl.c\
  $(LOCAL_DIR)/crypto/ec/ecx_backend.c\
  $(LOCAL_DIR)/crypto/ec/ecx_key.c\
  $(LOCAL_DIR)/crypto/ec/ecx_meth.c\
  $(LOCAL_DIR)/crypto/ec/ecx_s390x.c\
  $(LOCAL_DIR)/crypto/eia3/eia3.c\
  $(LOCAL_DIR)/crypto/encode_decode/decoder_err.c\
  $(LOCAL_DIR)/crypto/encode_decode/decoder_lib.c\
  $(LOCAL_DIR)/crypto/encode_decode/decoder_meth.c\
  $(LOCAL_DIR)/crypto/encode_decode/decoder_pkey.c\
  $(LOCAL_DIR)/crypto/encode_decode/encoder_err.c\
  $(LOCAL_DIR)/crypto/encode_decode/encoder_lib.c\
  $(LOCAL_DIR)/crypto/encode_decode/encoder_meth.c\
  $(LOCAL_DIR)/crypto/encode_decode/encoder_pkey.c\
  $(LOCAL_DIR)/crypto/engine/eng_all.c\
  $(LOCAL_DIR)/crypto/engine/eng_cnf.c\
  $(LOCAL_DIR)/crypto/engine/eng_ctrl.c\
  $(LOCAL_DIR)/crypto/engine/eng_dyn.c\
  $(LOCAL_DIR)/crypto/engine/eng_err.c\
  $(LOCAL_DIR)/crypto/engine/eng_fat.c\
  $(LOCAL_DIR)/crypto/engine/eng_init.c\
  $(LOCAL_DIR)/crypto/engine/eng_lib.c\
  $(LOCAL_DIR)/crypto/engine/eng_list.c\
  $(LOCAL_DIR)/crypto/engine/eng_openssl.c\
  $(LOCAL_DIR)/crypto/engine/eng_pkey.c\
  $(LOCAL_DIR)/crypto/engine/eng_rdrand.c\
  $(LOCAL_DIR)/crypto/engine/eng_table.c\
  $(LOCAL_DIR)/crypto/engine/tb_asnmth.c\
  $(LOCAL_DIR)/crypto/engine/tb_bnmeth.c\
  $(LOCAL_DIR)/crypto/engine/tb_cipher.c\
  $(LOCAL_DIR)/crypto/engine/tb_dh.c\
  $(LOCAL_DIR)/crypto/engine/tb_digest.c\
  $(LOCAL_DIR)/crypto/engine/tb_dsa.c\
  $(LOCAL_DIR)/crypto/engine/tb_eckey.c\
  $(LOCAL_DIR)/crypto/engine/tb_ecpmeth.c\
  $(LOCAL_DIR)/crypto/engine/tb_pkmeth.c\
  $(LOCAL_DIR)/crypto/engine/tb_rand.c\
  $(LOCAL_DIR)/crypto/engine/tb_rsa.c\
  $(LOCAL_DIR)/crypto/err/err.c\
  $(LOCAL_DIR)/crypto/err/err_all.c\
  $(LOCAL_DIR)/crypto/err/err_all_legacy.c\
  $(LOCAL_DIR)/crypto/err/err_blocks.c\
  $(LOCAL_DIR)/crypto/err/err_prn.c\
  $(LOCAL_DIR)/crypto/ess/ess_asn1.c\
  $(LOCAL_DIR)/crypto/ess/ess_err.c\
  $(LOCAL_DIR)/crypto/ess/ess_lib.c\
  $(LOCAL_DIR)/crypto/evp/asymcipher.c\
  $(LOCAL_DIR)/crypto/evp/bio_b64.c\
  $(LOCAL_DIR)/crypto/evp/bio_enc.c\
  $(LOCAL_DIR)/crypto/evp/bio_md.c\
  $(LOCAL_DIR)/crypto/evp/bio_ok.c\
  $(LOCAL_DIR)/crypto/evp/c_allc.c\
  $(LOCAL_DIR)/crypto/evp/c_alld.c\
  $(LOCAL_DIR)/crypto/evp/cmeth_lib.c\
  $(LOCAL_DIR)/crypto/evp/ctrl_params_translate.c\
  $(LOCAL_DIR)/crypto/evp/dh_ctrl.c\
  $(LOCAL_DIR)/crypto/evp/dh_support.c\
  $(LOCAL_DIR)/crypto/evp/digest.c\
  $(LOCAL_DIR)/crypto/evp/dsa_ctrl.c\
  $(LOCAL_DIR)/crypto/evp/e_aes.c\
  $(LOCAL_DIR)/crypto/evp/e_aes_cbc_hmac_sha1.c\
  $(LOCAL_DIR)/crypto/evp/e_aes_cbc_hmac_sha256.c\
  $(LOCAL_DIR)/crypto/evp/e_chacha20_poly1305.c\
  $(LOCAL_DIR)/crypto/evp/e_des.c\
  $(LOCAL_DIR)/crypto/evp/e_des3.c\
  $(LOCAL_DIR)/crypto/evp/e_eea3.c\
  $(LOCAL_DIR)/crypto/evp/e_null.c\
  $(LOCAL_DIR)/crypto/evp/e_old.c\
  $(LOCAL_DIR)/crypto/evp/e_rc4.c\
  $(LOCAL_DIR)/crypto/evp/e_rc4_hmac_md5.c\
  $(LOCAL_DIR)/crypto/evp/e_rc5.c\
  $(LOCAL_DIR)/crypto/evp/e_sm4.c\
  $(LOCAL_DIR)/crypto/evp/e_wbsm4_baiwu.c\
  $(LOCAL_DIR)/crypto/evp/e_wbsm4_wsise.c\
  $(LOCAL_DIR)/crypto/evp/e_wbsm4_xiaolai.c\
  $(LOCAL_DIR)/crypto/evp/e_xcbc_d.c\
  $(LOCAL_DIR)/crypto/evp/ec_ctrl.c\
  $(LOCAL_DIR)/crypto/evp/ec_support.c\
  $(LOCAL_DIR)/crypto/evp/encode.c\
  $(LOCAL_DIR)/crypto/evp/evp_cnf.c\
  $(LOCAL_DIR)/crypto/evp/evp_enc.c\
  $(LOCAL_DIR)/crypto/evp/evp_err.c\
  $(LOCAL_DIR)/crypto/evp/evp_fetch.c\
  $(LOCAL_DIR)/crypto/evp/evp_key.c\
  $(LOCAL_DIR)/crypto/evp/evp_lib.c\
  $(LOCAL_DIR)/crypto/evp/evp_pbe.c\
  $(LOCAL_DIR)/crypto/evp/evp_pkey.c\
  $(LOCAL_DIR)/crypto/evp/evp_rand.c\
  $(LOCAL_DIR)/crypto/evp/evp_utils.c\
  $(LOCAL_DIR)/crypto/evp/exchange.c\
  $(LOCAL_DIR)/crypto/evp/kdf_lib.c\
  $(LOCAL_DIR)/crypto/evp/kdf_meth.c\
  $(LOCAL_DIR)/crypto/evp/kem.c\
  $(LOCAL_DIR)/crypto/evp/keymgmt_lib.c\
  $(LOCAL_DIR)/crypto/evp/keymgmt_meth.c\
  $(LOCAL_DIR)/crypto/evp/legacy_md5.c\
  $(LOCAL_DIR)/crypto/evp/legacy_md5_sha1.c\
  $(LOCAL_DIR)/crypto/evp/legacy_sha.c\
  $(LOCAL_DIR)/crypto/evp/m_null.c\
  $(LOCAL_DIR)/crypto/evp/m_sigver.c\
  $(LOCAL_DIR)/crypto/evp/mac_lib.c\
  $(LOCAL_DIR)/crypto/evp/mac_meth.c\
  $(LOCAL_DIR)/crypto/evp/names.c\
  $(LOCAL_DIR)/crypto/evp/p5_crpt.c\
  $(LOCAL_DIR)/crypto/evp/p5_crpt2.c\
  $(LOCAL_DIR)/crypto/evp/p_dec.c\
  $(LOCAL_DIR)/crypto/evp/p_enc.c\
  $(LOCAL_DIR)/crypto/evp/p_legacy.c\
  $(LOCAL_DIR)/crypto/evp/p_lib.c\
  $(LOCAL_DIR)/crypto/evp/p_open.c\
  $(LOCAL_DIR)/crypto/evp/p_seal.c\
  $(LOCAL_DIR)/crypto/evp/p_sign.c\
  $(LOCAL_DIR)/crypto/evp/p_verify.c\
  $(LOCAL_DIR)/crypto/evp/pbe_scrypt.c\
  $(LOCAL_DIR)/crypto/evp/pmeth_check.c\
  $(LOCAL_DIR)/crypto/evp/pmeth_gn.c\
  $(LOCAL_DIR)/crypto/evp/pmeth_lib.c\
  $(LOCAL_DIR)/crypto/evp/signature.c\
  $(LOCAL_DIR)/crypto/ex_data.c\
  $(LOCAL_DIR)/crypto/ffc/ffc_backend.c\
  $(LOCAL_DIR)/crypto/ffc/ffc_dh.c\
  $(LOCAL_DIR)/crypto/ffc/ffc_key_generate.c\
  $(LOCAL_DIR)/crypto/ffc/ffc_key_validate.c\
  $(LOCAL_DIR)/crypto/ffc/ffc_params.c\
  $(LOCAL_DIR)/crypto/ffc/ffc_params_generate.c\
  $(LOCAL_DIR)/crypto/ffc/ffc_params_validate.c\
  $(LOCAL_DIR)/crypto/getenv.c\
  $(LOCAL_DIR)/crypto/hmac/hmac.c\
  $(LOCAL_DIR)/crypto/http/http_client.c\
  $(LOCAL_DIR)/crypto/http/http_err.c\
  $(LOCAL_DIR)/crypto/http/http_lib.c\
  $(LOCAL_DIR)/crypto/info.c\
  $(LOCAL_DIR)/crypto/init.c\
  $(LOCAL_DIR)/crypto/initthread.c\
  $(LOCAL_DIR)/crypto/kdf/kdf_err.c\
  $(LOCAL_DIR)/crypto/lhash/lh_stats.c\
  $(LOCAL_DIR)/crypto/lhash/lhash.c\
  $(LOCAL_DIR)/crypto/md5/md5_dgst.c\
  $(LOCAL_DIR)/crypto/md5/md5_one.c\
  $(LOCAL_DIR)/crypto/md5/md5_sha1.c\
  $(LOCAL_DIR)/crypto/mem.c\
  $(LOCAL_DIR)/crypto/mem_clr.c\
  $(LOCAL_DIR)/crypto/mem_sec.c\
  $(LOCAL_DIR)/crypto/modes/cbc128.c\
  $(LOCAL_DIR)/crypto/modes/ccm128.c\
  $(LOCAL_DIR)/crypto/modes/cfb128.c\
  $(LOCAL_DIR)/crypto/modes/ctr128.c\
  $(LOCAL_DIR)/crypto/modes/cts128.c\
  $(LOCAL_DIR)/crypto/modes/gcm128.c\
  $(LOCAL_DIR)/crypto/modes/ocb128.c\
  $(LOCAL_DIR)/crypto/modes/ofb128.c\
  $(LOCAL_DIR)/crypto/modes/siv128.c\
  $(LOCAL_DIR)/crypto/modes/wrap128.c\
  $(LOCAL_DIR)/crypto/modes/xts128.c\
  $(LOCAL_DIR)/crypto/o_dir.c\
  $(LOCAL_DIR)/crypto/o_fopen.c\
  $(LOCAL_DIR)/crypto/o_init.c\
  $(LOCAL_DIR)/crypto/o_str.c\
  $(LOCAL_DIR)/crypto/o_syslog.c\
  $(LOCAL_DIR)/crypto/o_time.c\
  $(LOCAL_DIR)/crypto/objects/o_names.c\
  $(LOCAL_DIR)/crypto/objects/obj_dat.c\
  $(LOCAL_DIR)/crypto/objects/obj_err.c\
  $(LOCAL_DIR)/crypto/objects/obj_lib.c\
  $(LOCAL_DIR)/crypto/objects/obj_xref.c\
  $(LOCAL_DIR)/crypto/ocsp/ocsp_asn.c\
  $(LOCAL_DIR)/crypto/ocsp/ocsp_cl.c\
  $(LOCAL_DIR)/crypto/ocsp/ocsp_err.c\
  $(LOCAL_DIR)/crypto/ocsp/ocsp_ext.c\
  $(LOCAL_DIR)/crypto/ocsp/ocsp_http.c\
  $(LOCAL_DIR)/crypto/ocsp/ocsp_lib.c\
  $(LOCAL_DIR)/crypto/ocsp/ocsp_prn.c\
  $(LOCAL_DIR)/crypto/ocsp/ocsp_srv.c\
  $(LOCAL_DIR)/crypto/ocsp/ocsp_vfy.c\
  $(LOCAL_DIR)/crypto/ocsp/v3_ocsp.c\
  $(LOCAL_DIR)/crypto/packet.c\
  $(LOCAL_DIR)/crypto/paillier/paillier_asn1.c\
  $(LOCAL_DIR)/crypto/paillier/paillier_crypt.c\
  $(LOCAL_DIR)/crypto/paillier/paillier_ctx.c\
  $(LOCAL_DIR)/crypto/paillier/paillier_encode.c\
  $(LOCAL_DIR)/crypto/paillier/paillier_key.c\
  $(LOCAL_DIR)/crypto/paillier/paillier_prn.c\
  $(LOCAL_DIR)/crypto/param_build.c\
  $(LOCAL_DIR)/crypto/param_build_set.c\
  $(LOCAL_DIR)/crypto/params.c\
  $(LOCAL_DIR)/crypto/params_dup.c\
  $(LOCAL_DIR)/crypto/params_from_text.c\
  $(LOCAL_DIR)/crypto/passphrase.c\
  $(LOCAL_DIR)/crypto/pem/pem_all.c\
  $(LOCAL_DIR)/crypto/pem/pem_err.c\
  $(LOCAL_DIR)/crypto/pem/pem_info.c\
  $(LOCAL_DIR)/crypto/pem/pem_lib.c\
  $(LOCAL_DIR)/crypto/pem/pem_oth.c\
  $(LOCAL_DIR)/crypto/pem/pem_pk8.c\
  $(LOCAL_DIR)/crypto/pem/pem_pkey.c\
  $(LOCAL_DIR)/crypto/pem/pem_sign.c\
  $(LOCAL_DIR)/crypto/pem/pem_x509.c\
  $(LOCAL_DIR)/crypto/pem/pem_xaux.c\
  $(LOCAL_DIR)/crypto/pem/pvkfmt.c\
  $(LOCAL_DIR)/crypto/pkcs12/p12_add.c\
  $(LOCAL_DIR)/crypto/pkcs12/p12_asn.c\
  $(LOCAL_DIR)/crypto/pkcs12/p12_attr.c\
  $(LOCAL_DIR)/crypto/pkcs12/p12_crpt.c\
  $(LOCAL_DIR)/crypto/pkcs12/p12_crt.c\
  $(LOCAL_DIR)/crypto/pkcs12/p12_decr.c\
  $(LOCAL_DIR)/crypto/pkcs12/p12_init.c\
  $(LOCAL_DIR)/crypto/pkcs12/p12_key.c\
  $(LOCAL_DIR)/crypto/pkcs12/p12_kiss.c\
  $(LOCAL_DIR)/crypto/pkcs12/p12_mutl.c\
  $(LOCAL_DIR)/crypto/pkcs12/p12_npas.c\
  $(LOCAL_DIR)/crypto/pkcs12/p12_p8d.c\
  $(LOCAL_DIR)/crypto/pkcs12/p12_p8e.c\
  $(LOCAL_DIR)/crypto/pkcs12/p12_sbag.c\
  $(LOCAL_DIR)/crypto/pkcs12/p12_utl.c\
  $(LOCAL_DIR)/crypto/pkcs12/pk12err.c\
  $(LOCAL_DIR)/crypto/pkcs7/bio_pk7.c\
  $(LOCAL_DIR)/crypto/pkcs7/pk7_asn1.c\
  $(LOCAL_DIR)/crypto/pkcs7/pk7_attr.c\
  $(LOCAL_DIR)/crypto/pkcs7/pk7_doit.c\
  $(LOCAL_DIR)/crypto/pkcs7/pk7_lib.c\
  $(LOCAL_DIR)/crypto/pkcs7/pk7_mime.c\
  $(LOCAL_DIR)/crypto/pkcs7/pk7_smime.c\
  $(LOCAL_DIR)/crypto/pkcs7/pkcs7err.c\
  $(LOCAL_DIR)/crypto/poly1305/poly1305.c\
  $(LOCAL_DIR)/crypto/poly1305/poly1305_base2_44.c\
  $(LOCAL_DIR)/crypto/poly1305/poly1305_ieee754.c\
  $(LOCAL_DIR)/crypto/poly1305/poly1305_ppc.c\
  $(LOCAL_DIR)/crypto/ppccap.c\
  $(LOCAL_DIR)/crypto/property/defn_cache.c\
  $(LOCAL_DIR)/crypto/property/property.c\
  $(LOCAL_DIR)/crypto/property/property_err.c\
  $(LOCAL_DIR)/crypto/property/property_parse.c\
  $(LOCAL_DIR)/crypto/property/property_query.c\
  $(LOCAL_DIR)/crypto/property/property_string.c\
  $(LOCAL_DIR)/crypto/provider.c\
  $(LOCAL_DIR)/crypto/provider_child.c\
  $(LOCAL_DIR)/crypto/provider_conf.c\
  $(LOCAL_DIR)/crypto/provider_core.c\
  $(LOCAL_DIR)/crypto/provider_predefined.c\
  $(LOCAL_DIR)/crypto/punycode.c\
  $(LOCAL_DIR)/crypto/rand/prov_seed.c\
  $(LOCAL_DIR)/crypto/rand/rand_deprecated.c\
  $(LOCAL_DIR)/crypto/rand/rand_egd.c\
  $(LOCAL_DIR)/crypto/rand/rand_err.c\
  $(LOCAL_DIR)/crypto/rand/rand_lib.c\
  $(LOCAL_DIR)/crypto/rand/rand_meth.c\
  $(LOCAL_DIR)/crypto/rand/rand_pool.c\
  $(LOCAL_DIR)/crypto/rand/randfile.c\
  $(LOCAL_DIR)/crypto/rc4/rc4_enc.c\
  $(LOCAL_DIR)/crypto/rc4/rc4_skey.c\
  $(LOCAL_DIR)/crypto/rc5/rc5_ecb.c\
  $(LOCAL_DIR)/crypto/rc5/rc5_enc.c\
  $(LOCAL_DIR)/crypto/rc5/rc5_skey.c\
  $(LOCAL_DIR)/crypto/rc5/rc5cfb64.c\
  $(LOCAL_DIR)/crypto/rc5/rc5ofb64.c\
  $(LOCAL_DIR)/crypto/rsa/rsa_acvp_test_params.c\
  $(LOCAL_DIR)/crypto/rsa/rsa_ameth.c\
  $(LOCAL_DIR)/crypto/rsa/rsa_asn1.c\
  $(LOCAL_DIR)/crypto/rsa/rsa_backend.c\
  $(LOCAL_DIR)/crypto/rsa/rsa_chk.c\
  $(LOCAL_DIR)/crypto/rsa/rsa_crpt.c\
  $(LOCAL_DIR)/crypto/rsa/rsa_depr.c\
  $(LOCAL_DIR)/crypto/rsa/rsa_err.c\
  $(LOCAL_DIR)/crypto/rsa/rsa_gen.c\
  $(LOCAL_DIR)/crypto/rsa/rsa_lib.c\
  $(LOCAL_DIR)/crypto/rsa/rsa_meth.c\
  $(LOCAL_DIR)/crypto/rsa/rsa_mp.c\
  $(LOCAL_DIR)/crypto/rsa/rsa_mp_names.c\
  $(LOCAL_DIR)/crypto/rsa/rsa_none.c\
  $(LOCAL_DIR)/crypto/rsa/rsa_oaep.c\
  $(LOCAL_DIR)/crypto/rsa/rsa_ossl.c\
  $(LOCAL_DIR)/crypto/rsa/rsa_pk1.c\
  $(LOCAL_DIR)/crypto/rsa/rsa_pmeth.c\
  $(LOCAL_DIR)/crypto/rsa/rsa_prn.c\
  $(LOCAL_DIR)/crypto/rsa/rsa_pss.c\
  $(LOCAL_DIR)/crypto/rsa/rsa_saos.c\
  $(LOCAL_DIR)/crypto/rsa/rsa_schemes.c\
  $(LOCAL_DIR)/crypto/rsa/rsa_sign.c\
  $(LOCAL_DIR)/crypto/rsa/rsa_sp800_56b_check.c\
  $(LOCAL_DIR)/crypto/rsa/rsa_sp800_56b_gen.c\
  $(LOCAL_DIR)/crypto/rsa/rsa_x931.c\
  $(LOCAL_DIR)/crypto/rsa/rsa_x931g.c\
  $(LOCAL_DIR)/crypto/s390xcap.c\
  $(LOCAL_DIR)/crypto/sdf/sdf_lib.c\
  $(LOCAL_DIR)/crypto/sdf/sdf_meth.c\
  $(LOCAL_DIR)/crypto/self_test_core.c\
  $(LOCAL_DIR)/crypto/sha/keccak1600.c\
  $(LOCAL_DIR)/crypto/sha/sha1_one.c\
  $(LOCAL_DIR)/crypto/sha/sha1dgst.c\
  $(LOCAL_DIR)/crypto/sha/sha256.c\
  $(LOCAL_DIR)/crypto/sha/sha3.c\
  $(LOCAL_DIR)/crypto/sha/sha512.c\
  $(LOCAL_DIR)/crypto/sha/sha_ppc.c\
  $(LOCAL_DIR)/crypto/siphash/siphash.c\
  $(LOCAL_DIR)/crypto/sm2/sm2_crypt.c\
  $(LOCAL_DIR)/crypto/sm2/sm2_err.c\
  $(LOCAL_DIR)/crypto/sm2/sm2_key.c\
  $(LOCAL_DIR)/crypto/sm2/sm2_kmeth.c\
  $(LOCAL_DIR)/crypto/sm2/sm2_sign.c\
  $(LOCAL_DIR)/crypto/sm2/sm2_threshold.c\
  $(LOCAL_DIR)/crypto/sm3/legacy_sm3.c\
  $(LOCAL_DIR)/crypto/sm3/sm3.c\
  $(LOCAL_DIR)/crypto/sm4/sm4.c\
  $(LOCAL_DIR)/crypto/sm4/wb/Bai-Wu-wbsm4.c\
  $(LOCAL_DIR)/crypto/sm4/wb/WBMatrix.c\
  $(LOCAL_DIR)/crypto/sm4/wb/WSISE-wbsm4.c\
  $(LOCAL_DIR)/crypto/sm4/wb/Xiao-Lai-wbsm4.c\
  $(LOCAL_DIR)/crypto/sm4/wb/wbsm4.c\
  $(LOCAL_DIR)/crypto/sparse_array.c\
  $(LOCAL_DIR)/crypto/srp/srp_lib.c\
  $(LOCAL_DIR)/crypto/srp/srp_vfy.c\
  $(LOCAL_DIR)/crypto/stack/stack.c\
  $(LOCAL_DIR)/crypto/store/store_err.c\
  $(LOCAL_DIR)/crypto/store/store_init.c\
  $(LOCAL_DIR)/crypto/store/store_lib.c\
  $(LOCAL_DIR)/crypto/store/store_meth.c\
  $(LOCAL_DIR)/crypto/store/store_register.c\
  $(LOCAL_DIR)/crypto/store/store_result.c\
  $(LOCAL_DIR)/crypto/store/store_strings.c\
  $(LOCAL_DIR)/crypto/threads_lib.c\
  $(LOCAL_DIR)/crypto/threads_none.c\
  $(LOCAL_DIR)/crypto/threads_pthread.c\
  $(LOCAL_DIR)/crypto/threads_win.c\
  $(LOCAL_DIR)/crypto/trace.c\
  $(LOCAL_DIR)/crypto/ts/ts_asn1.c\
  $(LOCAL_DIR)/crypto/ts/ts_conf.c\
  $(LOCAL_DIR)/crypto/ts/ts_err.c\
  $(LOCAL_DIR)/crypto/ts/ts_lib.c\
  $(LOCAL_DIR)/crypto/ts/ts_req_print.c\
  $(LOCAL_DIR)/crypto/ts/ts_req_utils.c\
  $(LOCAL_DIR)/crypto/ts/ts_rsp_print.c\
  $(LOCAL_DIR)/crypto/ts/ts_rsp_sign.c\
  $(LOCAL_DIR)/crypto/ts/ts_rsp_utils.c\
  $(LOCAL_DIR)/crypto/ts/ts_rsp_verify.c\
  $(LOCAL_DIR)/crypto/ts/ts_verify_ctx.c\
  $(LOCAL_DIR)/crypto/tsapi/tsapi_lib.c\
  $(LOCAL_DIR)/crypto/txt_db/txt_db.c\
  $(LOCAL_DIR)/crypto/ui/ui_err.c\
  $(LOCAL_DIR)/crypto/ui/ui_lib.c\
  $(LOCAL_DIR)/crypto/ui/ui_null.c\
  $(LOCAL_DIR)/crypto/ui/ui_openssl.c\
  $(LOCAL_DIR)/crypto/ui/ui_util.c\
  $(LOCAL_DIR)/crypto/uid.c\
  $(LOCAL_DIR)/crypto/x509/by_dir.c\
  $(LOCAL_DIR)/crypto/x509/by_file.c\
  $(LOCAL_DIR)/crypto/x509/by_store.c\
  $(LOCAL_DIR)/crypto/x509/pcy_cache.c\
  $(LOCAL_DIR)/crypto/x509/pcy_data.c\
  $(LOCAL_DIR)/crypto/x509/pcy_lib.c\
  $(LOCAL_DIR)/crypto/x509/pcy_map.c\
  $(LOCAL_DIR)/crypto/x509/pcy_node.c\
  $(LOCAL_DIR)/crypto/x509/pcy_tree.c\
  $(LOCAL_DIR)/crypto/x509/t_crl.c\
  $(LOCAL_DIR)/crypto/x509/t_req.c\
  $(LOCAL_DIR)/crypto/x509/t_x509.c\
  $(LOCAL_DIR)/crypto/x509/v3_addr.c\
  $(LOCAL_DIR)/crypto/x509/v3_admis.c\
  $(LOCAL_DIR)/crypto/x509/v3_akeya.c\
  $(LOCAL_DIR)/crypto/x509/v3_akid.c\
  $(LOCAL_DIR)/crypto/x509/v3_asid.c\
  $(LOCAL_DIR)/crypto/x509/v3_bcons.c\
  $(LOCAL_DIR)/crypto/x509/v3_bitst.c\
  $(LOCAL_DIR)/crypto/x509/v3_conf.c\
  $(LOCAL_DIR)/crypto/x509/v3_cpols.c\
  $(LOCAL_DIR)/crypto/x509/v3_crld.c\
  $(LOCAL_DIR)/crypto/x509/v3_enum.c\
  $(LOCAL_DIR)/crypto/x509/v3_extku.c\
  $(LOCAL_DIR)/crypto/x509/v3_genn.c\
  $(LOCAL_DIR)/crypto/x509/v3_ia5.c\
  $(LOCAL_DIR)/crypto/x509/v3_info.c\
  $(LOCAL_DIR)/crypto/x509/v3_int.c\
  $(LOCAL_DIR)/crypto/x509/v3_lib.c\
  $(LOCAL_DIR)/crypto/x509/v3_ncons.c\
  $(LOCAL_DIR)/crypto/x509/v3_pci.c\
  $(LOCAL_DIR)/crypto/x509/v3_pcia.c\
  $(LOCAL_DIR)/crypto/x509/v3_pcons.c\
  $(LOCAL_DIR)/crypto/x509/v3_pku.c\
  $(LOCAL_DIR)/crypto/x509/v3_pmaps.c\
  $(LOCAL_DIR)/crypto/x509/v3_prn.c\
  $(LOCAL_DIR)/crypto/x509/v3_purp.c\
  $(LOCAL_DIR)/crypto/x509/v3_san.c\
  $(LOCAL_DIR)/crypto/x509/v3_skid.c\
  $(LOCAL_DIR)/crypto/x509/v3_sxnet.c\
  $(LOCAL_DIR)/crypto/x509/v3_tlsf.c\
  $(LOCAL_DIR)/crypto/x509/v3_utl.c\
  $(LOCAL_DIR)/crypto/x509/v3err.c\
  $(LOCAL_DIR)/crypto/x509/x509_att.c\
  $(LOCAL_DIR)/crypto/x509/x509_cmp.c\
  $(LOCAL_DIR)/crypto/x509/x509_d2.c\
  $(LOCAL_DIR)/crypto/x509/x509_def.c\
  $(LOCAL_DIR)/crypto/x509/x509_err.c\
  $(LOCAL_DIR)/crypto/x509/x509_ext.c\
  $(LOCAL_DIR)/crypto/x509/x509_lu.c\
  $(LOCAL_DIR)/crypto/x509/x509_meth.c\
  $(LOCAL_DIR)/crypto/x509/x509_obj.c\
  $(LOCAL_DIR)/crypto/x509/x509_r2x.c\
  $(LOCAL_DIR)/crypto/x509/x509_req.c\
  $(LOCAL_DIR)/crypto/x509/x509_set.c\
  $(LOCAL_DIR)/crypto/x509/x509_trust.c\
  $(LOCAL_DIR)/crypto/x509/x509_txt.c\
  $(LOCAL_DIR)/crypto/x509/x509_v3.c\
  $(LOCAL_DIR)/crypto/x509/x509_vfy.c\
  $(LOCAL_DIR)/crypto/x509/x509_vpm.c\
  $(LOCAL_DIR)/crypto/x509/x509cset.c\
  $(LOCAL_DIR)/crypto/x509/x509name.c\
  $(LOCAL_DIR)/crypto/x509/x509rset.c\
  $(LOCAL_DIR)/crypto/x509/x509spki.c\
  $(LOCAL_DIR)/crypto/x509/x509type.c\
  $(LOCAL_DIR)/crypto/x509/x_all.c\
  $(LOCAL_DIR)/crypto/x509/x_attrib.c\
  $(LOCAL_DIR)/crypto/x509/x_crl.c\
  $(LOCAL_DIR)/crypto/x509/x_exten.c\
  $(LOCAL_DIR)/crypto/x509/x_name.c\
  $(LOCAL_DIR)/crypto/x509/x_pubkey.c\
  $(LOCAL_DIR)/crypto/x509/x_req.c\
  $(LOCAL_DIR)/crypto/x509/x_x509.c\
  $(LOCAL_DIR)/crypto/x509/x_x509a.c\
  $(LOCAL_DIR)/crypto/zkp/bulletproofs/bp_debug.c\
  $(LOCAL_DIR)/crypto/zkp/bulletproofs/bp_err.c\
  $(LOCAL_DIR)/crypto/zkp/bulletproofs/bulletproofs.c\
  $(LOCAL_DIR)/crypto/zkp/bulletproofs/bulletproofs_asn1.c\
  $(LOCAL_DIR)/crypto/zkp/bulletproofs/bulletproofs_encode.c\
  $(LOCAL_DIR)/crypto/zkp/bulletproofs/bulletproofs_prn.c\
  $(LOCAL_DIR)/crypto/zkp/bulletproofs/inner_product.c\
  $(LOCAL_DIR)/crypto/zkp/bulletproofs/r1cs.c\
  $(LOCAL_DIR)/crypto/zkp/bulletproofs/r1cs_constraint_expression.c\
  $(LOCAL_DIR)/crypto/zkp/bulletproofs/r1cs_linear_combination.c\
  $(LOCAL_DIR)/crypto/zkp/bulletproofs/range_proof.c\
  $(LOCAL_DIR)/crypto/zkp/common/zkp_debug.c\
  $(LOCAL_DIR)/crypto/zkp/common/zkp_err.c\
  $(LOCAL_DIR)/crypto/zkp/common/zkp_transcript.c\
  $(LOCAL_DIR)/crypto/zkp/common/zkp_transcript_sha256.c\
  $(LOCAL_DIR)/crypto/zkp/common/zkp_util.c\
  $(LOCAL_DIR)/crypto/zkp/gadget/zkp_range_proof.c\
  $(LOCAL_DIR)/crypto/zkp/nizk/nizk.c\
  $(LOCAL_DIR)/crypto/zkp/nizk/nizk_dlog_equality.c\
  $(LOCAL_DIR)/crypto/zkp/nizk/nizk_dlog_knowledge.c\
  $(LOCAL_DIR)/crypto/zkp/nizk/nizk_encode.c\
  $(LOCAL_DIR)/crypto/zkp/nizk/nizk_err.c\
  $(LOCAL_DIR)/crypto/zkp/nizk/nizk_plaintext_equality.c\
  $(LOCAL_DIR)/crypto/zkp/nizk/nizk_plaintext_knowledge.c\
  $(LOCAL_DIR)/crypto/zuc/zuc.c

ssl_sources := \
  $(LOCAL_DIR)/ssl/bio_ssl.c\
  $(LOCAL_DIR)/ssl/d1_lib.c\
  $(LOCAL_DIR)/ssl/d1_msg.c\
  $(LOCAL_DIR)/ssl/d1_srtp.c\
  $(LOCAL_DIR)/ssl/ktls.c\
  $(LOCAL_DIR)/ssl/methods.c\
  $(LOCAL_DIR)/ssl/pqueue.c\
  $(LOCAL_DIR)/ssl/record/dtls1_bitmap.c\
  $(LOCAL_DIR)/ssl/record/rec_layer_d1.c\
  $(LOCAL_DIR)/ssl/record/rec_layer_s3.c\
  $(LOCAL_DIR)/ssl/record/ssl3_buffer.c\
  $(LOCAL_DIR)/ssl/record/ssl3_record.c\
  $(LOCAL_DIR)/ssl/record/ssl3_record_tls13.c\
  $(LOCAL_DIR)/ssl/record/tls_pad.c\
  $(LOCAL_DIR)/ssl/s3_cbc.c\
  $(LOCAL_DIR)/ssl/s3_enc.c\
  $(LOCAL_DIR)/ssl/s3_lib.c\
  $(LOCAL_DIR)/ssl/s3_msg.c\
  $(LOCAL_DIR)/ssl/ssl_asn1.c\
  $(LOCAL_DIR)/ssl/ssl_cert.c\
  $(LOCAL_DIR)/ssl/ssl_ciph.c\
  $(LOCAL_DIR)/ssl/ssl_conf.c\
  $(LOCAL_DIR)/ssl/ssl_dc.c\
  $(LOCAL_DIR)/ssl/ssl_err.c\
  $(LOCAL_DIR)/ssl/ssl_err_legacy.c\
  $(LOCAL_DIR)/ssl/ssl_init.c\
  $(LOCAL_DIR)/ssl/ssl_lib.c\
  $(LOCAL_DIR)/ssl/ssl_mcnf.c\
  $(LOCAL_DIR)/ssl/ssl_quic.c\
  $(LOCAL_DIR)/ssl/ssl_rsa.c\
  $(LOCAL_DIR)/ssl/ssl_rsa_legacy.c\
  $(LOCAL_DIR)/ssl/ssl_sess.c\
  $(LOCAL_DIR)/ssl/ssl_stat.c\
  $(LOCAL_DIR)/ssl/ssl_txt.c\
  $(LOCAL_DIR)/ssl/ssl_utst.c\
  $(LOCAL_DIR)/ssl/statem/extensions.c\
  $(LOCAL_DIR)/ssl/statem/extensions_clnt.c\
  $(LOCAL_DIR)/ssl/statem/extensions_cust.c\
  $(LOCAL_DIR)/ssl/statem/extensions_srvr.c\
  $(LOCAL_DIR)/ssl/statem/statem.c\
  $(LOCAL_DIR)/ssl/statem/statem_clnt.c\
  $(LOCAL_DIR)/ssl/statem/statem_dtls.c\
  $(LOCAL_DIR)/ssl/statem/statem_lib.c\
  $(LOCAL_DIR)/ssl/statem/statem_quic.c\
  $(LOCAL_DIR)/ssl/statem/statem_srvr.c\
  $(LOCAL_DIR)/ssl/statem_ntls/ntls_extensions.c\
  $(LOCAL_DIR)/ssl/statem_ntls/ntls_extensions_clnt.c\
  $(LOCAL_DIR)/ssl/statem_ntls/ntls_extensions_cust.c\
  $(LOCAL_DIR)/ssl/statem_ntls/ntls_extensions_srvr.c\
  $(LOCAL_DIR)/ssl/statem_ntls/ntls_statem.c\
  $(LOCAL_DIR)/ssl/statem_ntls/ntls_statem_clnt.c\
  $(LOCAL_DIR)/ssl/statem_ntls/ntls_statem_lib.c\
  $(LOCAL_DIR)/ssl/statem_ntls/ntls_statem_srvr.c\
  $(LOCAL_DIR)/ssl/t1_enc.c\
  $(LOCAL_DIR)/ssl/t1_lib.c\
  $(LOCAL_DIR)/ssl/t1_trce.c\
  $(LOCAL_DIR)/ssl/tls13_enc.c\
  $(LOCAL_DIR)/ssl/tls_depr.c\
  $(LOCAL_DIR)/ssl/tls_srp.c

provider_sources := \
  $(LOCAL_DIR)/providers/baseprov.c\
  $(LOCAL_DIR)/providers/common/bio_prov.c\
  $(LOCAL_DIR)/providers/common/capabilities.c\
  $(LOCAL_DIR)/providers/common/der/der_digests_gen.c\
  $(LOCAL_DIR)/providers/common/der/der_dsa_gen.c\
  $(LOCAL_DIR)/providers/common/der/der_dsa_key.c\
  $(LOCAL_DIR)/providers/common/der/der_dsa_sig.c\
  $(LOCAL_DIR)/providers/common/der/der_ec_gen.c\
  $(LOCAL_DIR)/providers/common/der/der_ec_key.c\
  $(LOCAL_DIR)/providers/common/der/der_ec_sig.c\
  $(LOCAL_DIR)/providers/common/der/der_ecx_gen.c\
  $(LOCAL_DIR)/providers/common/der/der_ecx_key.c\
  $(LOCAL_DIR)/providers/common/der/der_rsa_gen.c\
  $(LOCAL_DIR)/providers/common/der/der_rsa_key.c\
  $(LOCAL_DIR)/providers/common/der/der_rsa_sig.c\
  $(LOCAL_DIR)/providers/common/der/der_sm2_gen.c\
  $(LOCAL_DIR)/providers/common/der/der_sm2_key.c\
  $(LOCAL_DIR)/providers/common/der/der_sm2_sig.c\
  $(LOCAL_DIR)/providers/common/der/der_wrap_gen.c\
  $(LOCAL_DIR)/providers/common/digest_to_nid.c\
  $(LOCAL_DIR)/providers/common/provider_ctx.c\
  $(LOCAL_DIR)/providers/common/provider_err.c\
  $(LOCAL_DIR)/providers/common/provider_seeding.c\
  $(LOCAL_DIR)/providers/common/provider_util.c\
  $(LOCAL_DIR)/providers/common/securitycheck.c\
  $(LOCAL_DIR)/providers/common/securitycheck_default.c\
  $(LOCAL_DIR)/providers/common/securitycheck_fips.c\
  $(LOCAL_DIR)/providers/defltprov.c\
  $(LOCAL_DIR)/providers/fips/fips_entry.c\
  $(LOCAL_DIR)/providers/fips/fipsprov.c\
  $(LOCAL_DIR)/providers/fips/self_test.c\
  $(LOCAL_DIR)/providers/fips/self_test_kats.c\
  $(LOCAL_DIR)/providers/implementations/asymciphers/rsa_enc.c\
  $(LOCAL_DIR)/providers/implementations/asymciphers/sm2_enc.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_aes.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_aes_cbc_hmac_sha.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_aes_cbc_hmac_sha1_hw.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_aes_cbc_hmac_sha256_hw.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_aes_ccm.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_aes_ccm_hw.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_aes_gcm.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_aes_gcm_hw.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_aes_hw.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_aes_ocb.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_aes_ocb_hw.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_aes_siv.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_aes_siv_hw.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_aes_wrp.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_aes_xts.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_aes_xts_fips.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_aes_xts_hw.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_chacha20.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_chacha20_hw.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_chacha20_poly1305.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_chacha20_poly1305_hw.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_cts.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_des.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_des_hw.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_desx.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_desx_hw.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_null.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_rc4.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_rc4_hmac_md5.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_rc4_hmac_md5_hw.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_rc4_hw.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_rc5.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_rc5_hw.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_sm4.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_sm4_ccm.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_sm4_ccm_hw.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_sm4_gcm.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_sm4_gcm_hw.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_sm4_hw.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_tdes.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_tdes_common.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_tdes_default.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_tdes_default_hw.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_tdes_hw.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_tdes_wrap.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_tdes_wrap_hw.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_wbsm4.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_wbsm4_ccm.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_wbsm4_ccm_hw.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_wbsm4_gcm.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_wbsm4_gcm_hw.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_wbsm4_hw.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_zuc_eea3.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/cipher_zuc_eea3_hw.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/ciphercommon.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/ciphercommon_block.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/ciphercommon_ccm.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/ciphercommon_ccm_hw.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/ciphercommon_gcm.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/ciphercommon_gcm_hw.c\
  $(LOCAL_DIR)/providers/implementations/ciphers/ciphercommon_hw.c\
  $(LOCAL_DIR)/providers/implementations/digests/digestcommon.c\
  $(LOCAL_DIR)/providers/implementations/digests/md5_prov.c\
  $(LOCAL_DIR)/providers/implementations/digests/md5_sha1_prov.c\
  $(LOCAL_DIR)/providers/implementations/digests/null_prov.c\
  $(LOCAL_DIR)/providers/implementations/digests/sha2_prov.c\
  $(LOCAL_DIR)/providers/implementations/digests/sha3_prov.c\
  $(LOCAL_DIR)/providers/implementations/digests/sm3_prov.c\
  $(LOCAL_DIR)/providers/implementations/encode_decode/decode_der2key.c\
  $(LOCAL_DIR)/providers/implementations/encode_decode/decode_epki2pki.c\
  $(LOCAL_DIR)/providers/implementations/encode_decode/decode_msblob2key.c\
  $(LOCAL_DIR)/providers/implementations/encode_decode/decode_pem2der.c\
  $(LOCAL_DIR)/providers/implementations/encode_decode/decode_pvk2key.c\
  $(LOCAL_DIR)/providers/implementations/encode_decode/decode_spki2typespki.c\
  $(LOCAL_DIR)/providers/implementations/encode_decode/encode_key2any.c\
  $(LOCAL_DIR)/providers/implementations/encode_decode/encode_key2blob.c\
  $(LOCAL_DIR)/providers/implementations/encode_decode/encode_key2ms.c\
  $(LOCAL_DIR)/providers/implementations/encode_decode/encode_key2text.c\
  $(LOCAL_DIR)/providers/implementations/encode_decode/endecoder_common.c\
  $(LOCAL_DIR)/providers/implementations/exchange/dh_exch.c\
  $(LOCAL_DIR)/providers/implementations/exchange/ecdh_exch.c\
  $(LOCAL_DIR)/providers/implementations/exchange/ecx_exch.c\
  $(LOCAL_DIR)/providers/implementations/exchange/kdf_exch.c\
  $(LOCAL_DIR)/providers/implementations/exchange/sm2dh_exch.c\
  $(LOCAL_DIR)/providers/implementations/kdfs/hkdf.c\
  $(LOCAL_DIR)/providers/implementations/kdfs/kbkdf.c\
  $(LOCAL_DIR)/providers/implementations/kdfs/krb5kdf.c\
  $(LOCAL_DIR)/providers/implementations/kdfs/pbkdf1.c\
  $(LOCAL_DIR)/providers/implementations/kdfs/pbkdf2.c\
  $(LOCAL_DIR)/providers/implementations/kdfs/pbkdf2_fips.c\
  $(LOCAL_DIR)/providers/implementations/kdfs/pkcs12kdf.c\
  $(LOCAL_DIR)/providers/implementations/kdfs/scrypt.c\
  $(LOCAL_DIR)/providers/implementations/kdfs/sshkdf.c\
  $(LOCAL_DIR)/providers/implementations/kdfs/sskdf.c\
  $(LOCAL_DIR)/providers/implementations/kdfs/tls1_prf.c\
  $(LOCAL_DIR)/providers/implementations/kdfs/wbsm4kdf.c\
  $(LOCAL_DIR)/providers/implementations/kdfs/x942kdf.c\
  $(LOCAL_DIR)/providers/implementations/kem/rsa_kem.c\
  $(LOCAL_DIR)/providers/implementations/keymgmt/dh_kmgmt.c\
  $(LOCAL_DIR)/providers/implementations/keymgmt/dsa_kmgmt.c\
  $(LOCAL_DIR)/providers/implementations/keymgmt/ec_kmgmt.c\
  $(LOCAL_DIR)/providers/implementations/keymgmt/ecx_kmgmt.c\
  $(LOCAL_DIR)/providers/implementations/keymgmt/kdf_legacy_kmgmt.c\
  $(LOCAL_DIR)/providers/implementations/keymgmt/mac_legacy_kmgmt.c\
  $(LOCAL_DIR)/providers/implementations/keymgmt/rsa_kmgmt.c\
  $(LOCAL_DIR)/providers/implementations/macs/cmac_prov.c\
  $(LOCAL_DIR)/providers/implementations/macs/eia3_prov.c\
  $(LOCAL_DIR)/providers/implementations/macs/gmac_prov.c\
  $(LOCAL_DIR)/providers/implementations/macs/hmac_prov.c\
  $(LOCAL_DIR)/providers/implementations/macs/kmac_prov.c\
  $(LOCAL_DIR)/providers/implementations/macs/poly1305_prov.c\
  $(LOCAL_DIR)/providers/implementations/macs/siphash_prov.c\
  $(LOCAL_DIR)/providers/implementations/rands/crngt.c\
  $(LOCAL_DIR)/providers/implementations/rands/drbg.c\
  $(LOCAL_DIR)/providers/implementations/rands/drbg_ctr.c\
  $(LOCAL_DIR)/providers/implementations/rands/drbg_hash.c\
  $(LOCAL_DIR)/providers/implementations/rands/drbg_hmac.c\
  $(LOCAL_DIR)/providers/implementations/rands/seed_src.c\
  $(LOCAL_DIR)/providers/implementations/rands/seeding/rand_cpu_x86.c\
  $(LOCAL_DIR)/providers/implementations/rands/seeding/rand_tsc.c\
  $(LOCAL_DIR)/providers/implementations/rands/seeding/rand_unix.c\
  $(LOCAL_DIR)/providers/implementations/rands/seeding/rand_vxworks.c\
  $(LOCAL_DIR)/providers/implementations/rands/seeding/rand_win.c\
  $(LOCAL_DIR)/providers/implementations/rands/smtc_rng.c\
  $(LOCAL_DIR)/providers/implementations/rands/test_rng.c\
  $(LOCAL_DIR)/providers/implementations/signature/dsa_sig.c\
  $(LOCAL_DIR)/providers/implementations/signature/ecdsa_sig.c\
  $(LOCAL_DIR)/providers/implementations/signature/eddsa_sig.c\
  $(LOCAL_DIR)/providers/implementations/signature/mac_legacy_sig.c\
  $(LOCAL_DIR)/providers/implementations/signature/rsa_sig.c\
  $(LOCAL_DIR)/providers/implementations/signature/sm2_sig.c\
  $(LOCAL_DIR)/providers/implementations/storemgmt/file_store.c\
  $(LOCAL_DIR)/providers/implementations/storemgmt/file_store_any2obj.c\
  $(LOCAL_DIR)/providers/legacyprov.c\
  $(LOCAL_DIR)/providers/nullprov.c\
  $(LOCAL_DIR)/providers/prov_running.c\
  $(LOCAL_DIR)/providers/smtc/self_test.c\
  $(LOCAL_DIR)/providers/smtc/self_test_kats.c\
  $(LOCAL_DIR)/providers/smtc/self_test_rand.c\
  $(LOCAL_DIR)/providers/smtc/smtc_entry.c\
  $(LOCAL_DIR)/providers/smtc/smtcprov.c

tongsuo_sources := $(crypto_sources) $(ssl_sources) $(provider_sources)
