# Copyright (C) 2014 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

LOCAL_DIR := $(GET_LOCAL_DIR)

MODULE := $(LOCAL_DIR)

# Configuration files are pre-generated

# Include source file definitions
include $(LOCAL_DIR)/sources.mk

MODULE_SRCS := $(crypto_sources)

MODULE_INCLUDES := \
	$(LOCAL_DIR)/include \
	$(LOCAL_DIR)/crypto \
	$(LOCAL_DIR)

MODULE_EXPORT_INCLUDES := \
	$(LOCAL_DIR)/include

# Compiler flags for Tongsuo
MODULE_CFLAGS := \
	-DOPENSSL_NO_ASM \
	-DOPENSSL_NO_SHARED \
	-DOPENSSL_STATIC_ARMCAP \
	-DOPENSSL_SMALL_FOOTPRINT \
	-DTRUSTY_USERSPACE \
	-DOPENSSL_API_COMPAT=0x30000000L \
	-DOPENSSL_CONFIGURED_API=30000 \
	-DSYMBOL_PREFIX=TONGSUO_ \
	-D__GNUC_PREREQ\(maj,min\)=\(\(__GNUC__\<\<16\)+__GNUC_MINOR__\>=\(\(maj\)\<\<16\)+\(min\)\) \
	-Wno-unused-parameter \
	-Wno-sign-compare \
	-Wno-unused-function

# Export include directory for Tongsuo headers
MODULE_EXPORT_INCLUDES += $(LOCAL_DIR)/include

# No additional module dependencies

include make/rctee_lib.mk
