#!/bin/bash

# Tongsuo 隔离构建脚本
# 解决系统OpenSSL头文件冲突问题
# 参考：https://www.tongsuo.net/docs/compilation/source-compilation

set -e

echo "=== Tongsuo 隔离构建脚本 ==="
echo "目标：生成集成构建所需的文件"
echo "工作目录：$(pwd)"

# 清理之前的构建文件
echo "清理之前的构建文件..."
make clean 2>/dev/null || true

# 设置隔离的构建环境，避免系统OpenSSL头文件干扰
echo "设置隔离构建环境..."
export PKG_CONFIG_PATH=""
export CPPFLAGS=""
export LDFLAGS=""

# 使用官方推荐的最简配置，明确指定不使用系统路径
echo "配置 Tongsuo 构建选项..."
echo "执行配置命令："
echo "./config --prefix=/tmp/tongsuo-build --openssldir=/tmp/tongsuo-build/ssl enable-sm2 enable-sm3 enable-sm4 enable-ntls --symbol-prefix=TONGSUO_ no-shared"

./config --prefix=/tmp/tongsuo-build --openssldir=/tmp/tongsuo-build/ssl enable-sm2 enable-sm3 enable-sm4 enable-ntls --symbol-prefix=TONGSUO_ no-shared

if [ $? -ne 0 ]; then
    echo "错误：配置失败"
    exit 1
fi

# 检查配置结果
echo "检查配置结果..."
perl configdata.pm --dump | head -30

# 修改Makefile以确保不包含系统OpenSSL路径
echo "修改编译器标志以避免系统头文件..."
sed -i 's|-I/usr/include||g' Makefile 2>/dev/null || true
sed -i 's|-I/usr/local/include||g' Makefile 2>/dev/null || true

# 编译库文件，使用明确的头文件路径
echo "编译 Tongsuo 库文件..."
make -j$(nproc) CPPFLAGS="-I. -Iinclude" build_libs

if [ $? -ne 0 ]; then
    echo "错误：编译失败"
    exit 1
fi

echo "=== 构建完成 ==="
echo "生成的库文件："
ls -la *.a 2>/dev/null || echo "未找到 .a 文件"
ls -la providers/*.a 2>/dev/null || echo "未找到 providers/*.a 文件"

echo "生成的头文件："
ls -la include/openssl/configuration.h 2>/dev/null || echo "未找到 configuration.h"
ls -la include/openssl/opensslv.h 2>/dev/null || echo "未找到 opensslv.h"
