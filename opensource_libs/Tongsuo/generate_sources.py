#!/usr/bin/env python3
"""
Generate sources.mk for Tongsuo integration with trusty-tee build system.
This script uses Tongsuo's official build system output to get the correct source files.
"""

import os
import re

def parse_crypto_sources_mk():
    """Parse the crypto-sources.mk file generated by Tongsuo."""
    crypto_files = []
    ssl_files = []

    crypto_mk_path = 'crypto-sources.mk'
    if os.path.exists(crypto_mk_path):
        with open(crypto_mk_path, 'r') as f:
            content = f.read()

        # Extract LIBCRYPTO_SRCS
        crypto_match = re.search(r'LIBCRYPTO_SRCS\s*=\s*(.*?)(?=\nLIBSSL_SRCS|\Z)', content, re.DOTALL)
        if crypto_match:
            crypto_content = crypto_match.group(1)
            crypto_files = re.findall(r'(\S+\.c)', crypto_content)

        # Extract LIBSSL_SRCS
        ssl_match = re.search(r'LIBSSL_SRCS\s*=\s*(.*?)(?=\n\w+\s*=|\Z)', content, re.DOTALL)
        if ssl_match:
            ssl_content = ssl_match.group(1)
            ssl_files = re.findall(r'(\S+\.c)', ssl_content)

    return crypto_files, ssl_files

def find_provider_files():
    """Find provider source files manually since they might not be in crypto-sources.mk."""
    provider_files = []
    providers_dir = 'providers'
    if os.path.exists(providers_dir):
        for root, dirs, files in os.walk(providers_dir):
            for file in files:
                if file.endswith('.c'):
                    rel_path = os.path.relpath(os.path.join(root, file), '.')
                    provider_files.append(rel_path)
    return sorted(provider_files)

def find_source_files_manual():
    """Find all .c source files manually, excluding problematic files."""
    crypto_sources = []
    ssl_sources = []

    # Excluded files that cause compilation issues
    excluded_files = {
        'crypto/asn1/x_delegated_credential.c',  # Delegated credential disabled
    }

    # Find all .c files in crypto directory
    for root, dirs, files in os.walk('crypto'):
        for file in files:
            if file.endswith('.c'):
                rel_path = os.path.relpath(os.path.join(root, file), '.')
                if rel_path not in excluded_files:
                    crypto_sources.append(rel_path)

    # Find all .c files in ssl directory
    for root, dirs, files in os.walk('ssl'):
        for file in files:
            if file.endswith('.c'):
                rel_path = os.path.relpath(os.path.join(root, file), '.')
                if rel_path not in excluded_files:
                    ssl_sources.append(rel_path)

    # Find provider files manually
    provider_sources = find_provider_files()

    # Sort for consistent output
    crypto_sources.sort()
    ssl_sources.sort()

    print(f"Found {len(crypto_sources)} crypto sources (manual scan)")
    print(f"Found {len(ssl_sources)} ssl sources (manual scan)")
    print(f"Found {len(provider_sources)} provider sources")

    return crypto_sources, ssl_sources, provider_sources

def find_source_files():
    """Find all .c source files using Tongsuo's official build system."""
    # Try to parse crypto-sources.mk generated by Tongsuo first
    crypto_sources, ssl_sources = parse_crypto_sources_mk()

    # If that fails, fall back to manual scanning
    if not crypto_sources and not ssl_sources:
        print("crypto-sources.mk not found or empty, falling back to manual scan")
        return find_source_files_manual()

    # Find provider files manually
    provider_sources = find_provider_files()

    print(f"Found {len(crypto_sources)} crypto sources from crypto-sources.mk")
    print(f"Found {len(ssl_sources)} ssl sources from crypto-sources.mk")
    print(f"Found {len(provider_sources)} provider sources")

    return crypto_sources, ssl_sources, provider_sources

def generate_sources_mk():
    """Generate sources.mk file following BoringSSL pattern."""
    crypto_sources, ssl_sources, provider_sources = find_source_files()

    with open('sources.mk', 'w') as f:
        f.write('# Copyright (c) 2024, Tongsuo Project\n')
        f.write('#\n')
        f.write('# Auto-generated sources.mk for Tongsuo integration with trusty-tee\n')
        f.write('# Generated by generate_sources.py. Do not edit manually.\n\n')

        # Write crypto sources
        f.write('crypto_sources := \\\n')
        for i, src in enumerate(crypto_sources):
            # Remove $(LOCAL_DIR)/ prefix if present and add it back correctly
            clean_src = src.replace('$(LOCAL_DIR)/', '').replace('$$(LOCAL_DIR)/', '')
            if i == len(crypto_sources) - 1:
                f.write(f'  $(LOCAL_DIR)/{clean_src}\n\n')
            else:
                f.write(f'  $(LOCAL_DIR)/{clean_src}\\\n')

        # Write ssl sources
        f.write('ssl_sources := \\\n')
        for i, src in enumerate(ssl_sources):
            # Remove $(LOCAL_DIR)/ prefix if present and add it back correctly
            clean_src = src.replace('$(LOCAL_DIR)/', '').replace('$$(LOCAL_DIR)/', '')
            if i == len(ssl_sources) - 1:
                f.write(f'  $(LOCAL_DIR)/{clean_src}\n\n')
            else:
                f.write(f'  $(LOCAL_DIR)/{clean_src}\\\n')

        # Write provider sources
        f.write('provider_sources := \\\n')
        for i, src in enumerate(provider_sources):
            # Remove $(LOCAL_DIR)/ prefix if present and add it back correctly
            clean_src = src.replace('$(LOCAL_DIR)/', '').replace('$$(LOCAL_DIR)/', '')
            if i == len(provider_sources) - 1:
                f.write(f'  $(LOCAL_DIR)/{clean_src}\n\n')
            else:
                f.write(f'  $(LOCAL_DIR)/{clean_src}\\\n')

        # Combined sources
        f.write('tongsuo_sources := $(crypto_sources) $(ssl_sources) $(provider_sources)\n')

if __name__ == '__main__':
    generate_sources_mk()
    print("Generated sources.mk successfully")
